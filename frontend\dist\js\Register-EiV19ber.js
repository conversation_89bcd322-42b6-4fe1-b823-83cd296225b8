import{u as z,r as w,z as C,c as A,a as l,b as B,d as t,w as o,A as N,e as f,n as u,l as S,m as F,f as m,B as b,C as M,q as c,E as _,G as T,H as Z}from"./index-D4DFMkO1.js";import{_ as $}from"./_plugin-vue_export-helper-DlAUqK2U.js";const q={class:"auth-page"},D={class:"auth-container"},G={class:"auth-card card"},H={class:"auth-footer"},I={__name:"Register",setup(j){const x=S(),E=z(),p=w(!1),v=w(),s=C({username:"",email:"",password:"",confirmPassword:"",agreement:!1}),V={username:[{validator:async(i,e,r)=>{if(!e){r(new Error("请输入用户名"));return}if(e.length<2||e.length>20){r(new Error("用户名长度必须在2-20个字符之间"));return}if(!/^[a-zA-Z0-9_]+$/.test(e)){r(new Error("用户名只能包含字母、数字和下划线"));return}try{const a=await Z(e);a.data||a?r(new Error("用户名已存在")):r()}catch{r()}},trigger:"blur"}],email:[{validator:async(i,e,r)=>{if(!e){r(new Error("请输入邮箱"));return}if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)){r(new Error("请输入正确的邮箱格式"));return}try{const n=await T(e);n.data||n?r(new Error("邮箱已被注册")):r()}catch{r()}},trigger:"blur"}],password:[{validator:(i,e,r)=>{if(!e){r(new Error("请输入密码"));return}if(e.length<6){r(new Error("密码长度不能少于6位"));return}if(!/(?=.*[A-Za-z])(?=.*\d)/.test(e)){r(new Error("密码必须包含字母和数字"));return}r()},trigger:"blur"}],confirmPassword:[{validator:(i,e,r)=>{if(!e){r(new Error("请确认密码"));return}if(e!==s.password){r(new Error("两次输入的密码不一致"));return}r()},trigger:"blur"}],agreement:[{validator:(i,e,r)=>{if(!e){r(new Error("请阅读并同意服务条款和隐私政策"));return}r()},trigger:"change"}]},g=async()=>{if(v.value)try{await v.value.validate(),p.value=!0;const{confirmPassword:i,agreement:e,...r}=s;await E.registerUser(r)&&x.push("/login")}catch(i){console.error("Register validation failed:",i)}finally{p.value=!1}},y=()=>{_.info("服务条款功能待实现")},h=()=>{_.info("隐私政策功能待实现")};return(i,e)=>{const r=f("el-input"),a=f("el-form-item"),n=f("el-button"),P=f("el-checkbox"),R=f("el-form"),U=f("router-link");return F(),A("div",q,[l("div",D,[l("div",G,[e[12]||(e[12]=l("div",{class:"auth-header"},[l("h2",null,"创建账户"),l("p",null,"加入我们，开始您的心理健康之旅")],-1)),t(R,{ref_key:"registerFormRef",ref:v,model:s,rules:V,class:"auth-form",onSubmit:N(g,["prevent"])},{default:o(()=>[t(a,{prop:"username"},{default:o(()=>[t(r,{modelValue:s.username,"onUpdate:modelValue":e[0]||(e[0]=d=>s.username=d),placeholder:"请输入用户名",size:"large","prefix-icon":m(b)},null,8,["modelValue","prefix-icon"])]),_:1}),t(a,{prop:"email"},{default:o(()=>[t(r,{modelValue:s.email,"onUpdate:modelValue":e[1]||(e[1]=d=>s.email=d),placeholder:"请输入邮箱",size:"large","prefix-icon":m(M)},null,8,["modelValue","prefix-icon"])]),_:1}),t(a,{prop:"password"},{default:o(()=>[t(r,{modelValue:s.password,"onUpdate:modelValue":e[2]||(e[2]=d=>s.password=d),type:"password",placeholder:"请输入密码",size:"large","prefix-icon":m(c),"show-password":""},null,8,["modelValue","prefix-icon"])]),_:1}),t(a,{prop:"confirmPassword"},{default:o(()=>[t(r,{modelValue:s.confirmPassword,"onUpdate:modelValue":e[3]||(e[3]=d=>s.confirmPassword=d),type:"password",placeholder:"请确认密码",size:"large","prefix-icon":m(c),"show-password":""},null,8,["modelValue","prefix-icon"])]),_:1}),t(a,{prop:"agreement"},{default:o(()=>[t(P,{modelValue:s.agreement,"onUpdate:modelValue":e[4]||(e[4]=d=>s.agreement=d)},{default:o(()=>[e[7]||(e[7]=u(" 我已阅读并同意 ",-1)),t(n,{type:"text",onClick:y},{default:o(()=>[...e[5]||(e[5]=[u("《服务条款》",-1)])]),_:1}),e[8]||(e[8]=u(" 和 ",-1)),t(n,{type:"text",onClick:h},{default:o(()=>[...e[6]||(e[6]=[u("《隐私政策》",-1)])]),_:1})]),_:1},8,["modelValue"])]),_:1}),t(a,null,{default:o(()=>[t(n,{type:"primary",size:"large",loading:p.value,onClick:g,class:"auth-button"},{default:o(()=>[...e[9]||(e[9]=[u(" 注册 ",-1)])]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"]),l("div",H,[l("p",null,[e[11]||(e[11]=u("已有账户？ ",-1)),t(U,{to:"/login",class:"auth-link"},{default:o(()=>[...e[10]||(e[10]=[u("立即登录",-1)])]),_:1})])])]),e[13]||(e[13]=B('<div class="auth-side" data-v-305df092><div class="side-content" data-v-305df092><h3 data-v-305df092>开始您的心理健康之旅</h3><p data-v-305df092>注册账户，享受专业的心理咨询服务</p><div class="side-stats" data-v-305df092><div class="stat-item" data-v-305df092><div class="stat-number" data-v-305df092>1000+</div><div class="stat-label" data-v-305df092>专业咨询师</div></div><div class="stat-item" data-v-305df092><div class="stat-number" data-v-305df092>50000+</div><div class="stat-label" data-v-305df092>服务用户</div></div><div class="stat-item" data-v-305df092><div class="stat-number" data-v-305df092>98%</div><div class="stat-label" data-v-305df092>满意度</div></div></div></div></div>',1))])])}}},Y=$(I,[["__scopeId","data-v-305df092"]]);export{Y as default};
