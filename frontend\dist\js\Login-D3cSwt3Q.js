import{u as E,r as i,z as U,c as M,a as s,d as o,w as l,A as N,e as a,n as r,l as S,m as A,f as u,B as D,q as R,v as I,t as T,C as $,D as j,E as G}from"./index-D4DFMkO1.js";import{_ as H}from"./_plugin-vue_export-helper-DlAUqK2U.js";const J={class:"auth-page"},K={class:"auth-container"},Q={class:"auth-card card"},W={class:"auth-options"},X={class:"auth-footer"},Y={class:"auth-side"},Z={class:"side-content"},ee={class:"side-features"},oe={class:"side-feature"},se={class:"side-feature"},le={class:"side-feature"},te={__name:"Login",setup(ae){const P=S(),C=E(),_=i(!1),c=i(!1),x=i(!1),d=i(!1),v=i(),w=i(),n=U({emailOrUsername:"",password:""}),m=U({email:""}),F={emailOrUsername:[{required:!0,message:"请输入邮箱或用户名",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,message:"密码长度不能少于6位",trigger:"blur"}]},L={email:[{required:!0,message:"请输入邮箱",trigger:"blur"},{type:"email",message:"请输入正确的邮箱格式",trigger:"blur"}]},y=async()=>{if(v.value)try{await v.value.validate(),_.value=!0,await C.loginUser(n)&&P.push("/dashboard")}catch(f){console.error("Login validation failed:",f)}finally{_.value=!1}},O=()=>{d.value=!0},q=async()=>{if(w.value)try{await w.value.validate(),c.value=!0,await j(m),G.success("重置密码邮件已发送，请查收邮箱"),d.value=!1,m.email=""}catch(f){console.error("Reset password failed:",f)}finally{c.value=!1}};return(f,e)=>{const V=a("el-input"),p=a("el-form-item"),z=a("el-checkbox"),g=a("el-button"),b=a("el-form"),B=a("router-link"),k=a("el-icon"),h=a("el-dialog");return A(),M("div",J,[s("div",K,[s("div",Q,[e[11]||(e[11]=s("div",{class:"auth-header"},[s("h2",null,"欢迎回来"),s("p",null,"登录您的账户继续使用服务")],-1)),o(b,{ref_key:"loginFormRef",ref:v,model:n,rules:F,class:"auth-form",onSubmit:N(y,["prevent"])},{default:l(()=>[o(p,{prop:"emailOrUsername"},{default:l(()=>[o(V,{modelValue:n.emailOrUsername,"onUpdate:modelValue":e[0]||(e[0]=t=>n.emailOrUsername=t),placeholder:"请输入邮箱或用户名",size:"large","prefix-icon":u(D)},null,8,["modelValue","prefix-icon"])]),_:1}),o(p,{prop:"password"},{default:l(()=>[o(V,{modelValue:n.password,"onUpdate:modelValue":e[1]||(e[1]=t=>n.password=t),type:"password",placeholder:"请输入密码",size:"large","prefix-icon":u(R),"show-password":""},null,8,["modelValue","prefix-icon"])]),_:1}),s("div",W,[o(z,{modelValue:x.value,"onUpdate:modelValue":e[2]||(e[2]=t=>x.value=t)},{default:l(()=>[...e[6]||(e[6]=[r("记住我",-1)])]),_:1},8,["modelValue"]),o(g,{type:"text",onClick:O},{default:l(()=>[...e[7]||(e[7]=[r("忘记密码？",-1)])]),_:1})]),o(p,null,{default:l(()=>[o(g,{type:"primary",size:"large",loading:_.value,onClick:y,class:"auth-button"},{default:l(()=>[...e[8]||(e[8]=[r(" 登录 ",-1)])]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"]),s("div",X,[s("p",null,[e[10]||(e[10]=r("还没有账户？ ",-1)),o(B,{to:"/register",class:"auth-link"},{default:l(()=>[...e[9]||(e[9]=[r("立即注册",-1)])]),_:1})])])]),s("div",Y,[s("div",Z,[e[15]||(e[15]=s("h3",null,"专业心理咨询服务",-1)),e[16]||(e[16]=s("p",null,"连接专业咨询师，获得个性化心理健康支持",-1)),s("div",ee,[s("div",oe,[o(k,null,{default:l(()=>[o(u(R))]),_:1}),e[12]||(e[12]=s("span",null,"隐私保护",-1))]),s("div",se,[o(k,null,{default:l(()=>[o(u(I))]),_:1}),e[13]||(e[13]=s("span",null,"专业认证",-1))]),s("div",le,[o(k,null,{default:l(()=>[o(u(T))]),_:1}),e[14]||(e[14]=s("span",null,"灵活预约",-1))])])])])]),o(h,{modelValue:d.value,"onUpdate:modelValue":e[5]||(e[5]=t=>d.value=t),title:"重置密码",width:"400px"},{footer:l(()=>[o(g,{onClick:e[4]||(e[4]=t=>d.value=!1)},{default:l(()=>[...e[17]||(e[17]=[r("取消",-1)])]),_:1}),o(g,{type:"primary",loading:c.value,onClick:q},{default:l(()=>[...e[18]||(e[18]=[r(" 发送重置邮件 ",-1)])]),_:1},8,["loading"])]),default:l(()=>[o(b,{model:m,rules:L,ref_key:"forgotPasswordFormRef",ref:w},{default:l(()=>[o(p,{prop:"email"},{default:l(()=>[o(V,{modelValue:m.email,"onUpdate:modelValue":e[3]||(e[3]=t=>m.email=t),placeholder:"请输入注册邮箱","prefix-icon":u($)},null,8,["modelValue","prefix-icon"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},ie=H(te,[["__scopeId","data-v-154cc938"]]);export{ie as default};
