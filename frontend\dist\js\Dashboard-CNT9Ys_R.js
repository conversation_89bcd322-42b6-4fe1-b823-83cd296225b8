import{u as A,S as C,L as B,c as g,a as e,d as s,w as a,f as l,e as r,y as u,F as T,k as V,l as F,m as i,n as d,g as k,T as I,U as L,V as R,W as z}from"./index-D4DFMkO1.js";import{_ as H}from"./_plugin-vue_export-helper-DlAUqK2U.js";const P={class:"dashboard"},E={class:"sidebar"},W={class:"sidebar-header"},$={class:"user-info"},j={class:"user-details"},q={class:"sidebar-nav"},G={class:"sidebar-footer"},J={class:"main-content"},K={class:"content-header"},M={class:"header-left"},O={class:"header-right"},Q={class:"content-body"},X={__name:"Dashboard",setup(Y){const p=F(),y=B(),n=A(),h=C(()=>{const t=[{name:"DashboardHome",path:"/dashboard",label:"概览",icon:"TrendCharts"},{name:"Profile",path:"/dashboard/profile",label:"个人信息",icon:"User"},{name:"Appointments",path:"/dashboard/appointments",label:"我的预约",icon:"Calendar"},{name:"Sessions",path:"/dashboard/sessions",label:"咨询记录",icon:"ChatDotRound"}];return n.isCounselor?t.push({name:"CounselorCenter",path:"/dashboard/counselor-center",label:"咨询师中心",icon:"Setting"}):t.push({name:"CounselorApply",path:"/dashboard/counselor-apply",label:"申请成为咨询师",icon:"UserFilled"}),t}),w=C(()=>{const t=h.value.find(o=>o.name===y.name);return(t==null?void 0:t.label)||"个人中心"}),D=()=>{p.push("/")},N=async()=>{await n.logoutUser(),p.push("/")};return(t,o)=>{var v;const S=r("el-avatar"),_=r("el-icon"),b=r("router-link"),f=r("el-button"),m=r("el-breadcrumb-item"),x=r("el-breadcrumb"),U=r("router-view");return i(),g("div",P,[e("aside",E,[e("div",W,[e("div",$,[s(S,{size:60,src:l(n).userAvatar},{default:a(()=>[d(u(l(n).userName.charAt(0)),1)]),_:1},8,["src"]),e("div",j,[e("h3",null,u(((v=l(n).userInfo)==null?void 0:v.realName)||l(n).userName),1),e("p",null,u(l(n).isCounselor?"咨询师":"用户"),1)])])]),e("nav",q,[(i(!0),g(T,null,V(h.value,c=>(i(),k(b,{key:c.name,to:c.path,class:L(["nav-item",{active:t.$route.name===c.name}])},{default:a(()=>[s(_,null,{default:a(()=>[(i(),k(I(c.icon)))]),_:2},1024),e("span",null,u(c.label),1)]),_:2},1032,["to","class"]))),128))]),e("div",G,[s(f,{onClick:N,class:"logout-btn"},{default:a(()=>[s(_,null,{default:a(()=>[s(l(R))]),_:1}),o[0]||(o[0]=d(" 退出登录 ",-1))]),_:1})])]),e("main",J,[e("header",K,[e("div",M,[s(x,{separator:"/"},{default:a(()=>[s(m,null,{default:a(()=>[s(b,{to:"/"},{default:a(()=>[...o[1]||(o[1]=[d("首页",-1)])]),_:1})]),_:1}),s(m,null,{default:a(()=>[...o[2]||(o[2]=[d("个人中心",-1)])]),_:1}),s(m,null,{default:a(()=>[d(u(w.value),1)]),_:1})]),_:1})]),e("div",O,[s(f,{onClick:D,type:"text"},{default:a(()=>[s(_,null,{default:a(()=>[s(l(z))]),_:1}),o[3]||(o[3]=d(" 返回首页 ",-1))]),_:1})])]),e("div",Q,[s(U)])])])}}},se=H(X,[["__scopeId","data-v-b01155cc"]]);export{se as default};
