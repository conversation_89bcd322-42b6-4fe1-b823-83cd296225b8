<template>
  <div class="profile-container">
    <div class="profile-header">
      <h2>个人信息</h2>
      <p class="subtitle">管理您的个人资料和账户设置</p>
    </div>

    <div class="profile-content">
      <!-- 头像部分 -->
      <div class="avatar-section">
        <div class="avatar-wrapper">
          <img 
            :src="userInfo.avatarUrl || '/default-avatar.png'" 
            :alt="userInfo.username"
            class="avatar"
          >
          <button class="avatar-upload-btn" @click="handleAvatarUpload">
            <i class="icon-camera"></i>
            更换头像
          </button>
        </div>
      </div>

      <!-- 基本信息表单 -->
      <div class="form-section">
        <form @submit.prevent="handleSubmit" class="profile-form">
          <div class="form-group">
            <label for="username">用户名</label>
            <input 
              id="username"
              v-model="form.username" 
              type="text" 
              class="form-input"
              readonly
            >
          </div>

          <div class="form-group">
            <label for="email">邮箱</label>
            <input 
              id="email"
              v-model="form.email" 
              type="email" 
              class="form-input"
              readonly
            >
          </div>

          <div class="form-group">
            <label for="realName">真实姓名</label>
            <input 
              id="realName"
              v-model="form.realName" 
              type="text" 
              class="form-input"
              placeholder="请输入真实姓名"
            >
          </div>

          <div class="form-group">
            <label for="phone">手机号</label>
            <input 
              id="phone"
              v-model="form.phone" 
              type="tel" 
              class="form-input"
              placeholder="请输入手机号"
            >
          </div>

          <div class="form-group">
            <label for="gender">性别</label>
            <select id="gender" v-model="form.gender" class="form-select">
              <option value="">请选择性别</option>
              <option value="MALE">男</option>
              <option value="FEMALE">女</option>
              <option value="OTHER">其他</option>
            </select>
          </div>

          <div class="form-group">
            <label for="birthDate">出生日期</label>
            <input 
              id="birthDate"
              v-model="form.birthDate" 
              type="date" 
              class="form-input"
            >
          </div>

          <div class="form-group">
            <label for="bio">个人简介</label>
            <textarea 
              id="bio"
              v-model="form.bio" 
              class="form-textarea"
              placeholder="介绍一下自己..."
              rows="4"
            ></textarea>
          </div>

          <div class="form-actions">
            <button type="submit" class="btn btn-primary" :disabled="loading">
              <span v-if="loading">保存中...</span>
              <span v-else>保存更改</span>
            </button>
            <button type="button" class="btn btn-secondary" @click="resetForm">
              重置
            </button>
          </div>
        </form>
      </div>

      <!-- 密码修改部分 -->
      <div class="password-section">
        <h3>修改密码</h3>
        <form @submit.prevent="handlePasswordChange" class="password-form">
          <div class="form-group">
            <label for="currentPassword">当前密码</label>
            <input 
              id="currentPassword"
              v-model="passwordForm.currentPassword" 
              type="password" 
              class="form-input"
              placeholder="请输入当前密码"
            >
          </div>

          <div class="form-group">
            <label for="newPassword">新密码</label>
            <input 
              id="newPassword"
              v-model="passwordForm.newPassword" 
              type="password" 
              class="form-input"
              placeholder="请输入新密码"
            >
          </div>

          <div class="form-group">
            <label for="confirmPassword">确认新密码</label>
            <input 
              id="confirmPassword"
              v-model="passwordForm.confirmPassword" 
              type="password" 
              class="form-input"
              placeholder="请再次输入新密码"
            >
          </div>

          <div class="form-actions">
            <button type="submit" class="btn btn-primary" :disabled="passwordLoading">
              <span v-if="passwordLoading">修改中...</span>
              <span v-else>修改密码</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'

export default {
  name: 'Profile',
  setup() {
    const userStore = useUserStore()
    const loading = ref(false)
    const passwordLoading = ref(false)

    // 用户信息表单
    const form = reactive({
      username: '',
      email: '',
      realName: '',
      phone: '',
      gender: '',
      birthDate: '',
      bio: ''
    })

    // 密码修改表单
    const passwordForm = reactive({
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    })

    // 用户信息
    const userInfo = reactive({
      username: '',
      email: '',
      avatarUrl: ''
    })

    // 初始化数据
    const initData = () => {
      const user = userStore.user
      if (user) {
        Object.assign(form, {
          username: user.username || '',
          email: user.email || '',
          realName: user.realName || '',
          phone: user.phone || '',
          gender: user.gender || '',
          birthDate: user.birthDate || '',
          bio: user.bio || ''
        })
        
        Object.assign(userInfo, {
          username: user.username || '',
          email: user.email || '',
          avatarUrl: user.avatarUrl || ''
        })
      }
    }

    // 提交个人信息
    const handleSubmit = async () => {
      loading.value = true
      try {
        // TODO: 调用API更新用户信息
        await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟API调用
        ElMessage.success('个人信息更新成功')
      } catch (error) {
        ElMessage.error('更新失败，请重试')
      } finally {
        loading.value = false
      }
    }

    // 重置表单
    const resetForm = () => {
      initData()
    }

    // 头像上传
    const handleAvatarUpload = () => {
      // TODO: 实现头像上传功能
      ElMessage.info('头像上传功能开发中...')
    }

    // 修改密码
    const handlePasswordChange = async () => {
      if (passwordForm.newPassword !== passwordForm.confirmPassword) {
        ElMessage.error('两次输入的密码不一致')
        return
      }

      passwordLoading.value = true
      try {
        // TODO: 调用API修改密码
        await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟API调用
        ElMessage.success('密码修改成功')
        
        // 清空表单
        Object.assign(passwordForm, {
          currentPassword: '',
          newPassword: '',
          confirmPassword: ''
        })
      } catch (error) {
        ElMessage.error('密码修改失败，请重试')
      } finally {
        passwordLoading.value = false
      }
    }

    onMounted(() => {
      initData()
    })

    return {
      form,
      passwordForm,
      userInfo,
      loading,
      passwordLoading,
      handleSubmit,
      resetForm,
      handleAvatarUpload,
      handlePasswordChange
    }
  }
}
</script>

<style scoped>
.profile-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 24px;
}

.profile-header {
  margin-bottom: 32px;
}

.profile-header h2 {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8px;
}

.subtitle {
  color: #6b7280;
  font-size: 14px;
}

.profile-content {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.avatar-section {
  display: flex;
  justify-content: center;
  padding: 24px;
  background: #f9fafb;
  border-radius: 12px;
}

.avatar-wrapper {
  text-align: center;
}

.avatar {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid #fff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
}

.avatar-upload-btn {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.avatar-upload-btn:hover {
  background: #2563eb;
}

.form-section,
.password-section {
  background: white;
  padding: 24px;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
}

.password-section h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: #374151;
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input[readonly] {
  background-color: #f9fafb;
  color: #6b7280;
}

.form-actions {
  display: flex;
  gap: 12px;
  margin-top: 24px;
}

.btn {
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #2563eb;
}

.btn-secondary {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn-secondary:hover {
  background: #e5e7eb;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
</style>
