import{K,u as X,r as D,o as $,c as d,a as e,i as W,d as s,w as a,e as c,f as u,F as w,j as Z,h,n,y as i,L as tt,E as v,M as et,l as st,m as _,N as ot,v as at,I as lt,J as nt,O as it,x as ut,C as rt,P as dt,Q as ct,R as _t}from"./index-D4DFMkO1.js";import{a as pt}from"./counselor-De_gGA3T.js";import{_ as vt}from"./_plugin-vue_export-helper-DlAUqK2U.js";const mt=N=>K({url:"/appointments",method:"post",data:N}),ft={class:"counselor-detail-page"},gt={class:"navbar"},yt={class:"nav-container"},Et={class:"nav-menu"},kt={class:"page-container"},Dt={key:0,class:"content-wrapper"},bt={class:"counselor-header card"},ht={class:"counselor-avatar"},Nt={key:0,class:"availability-badge"},St={class:"counselor-info"},Ct={class:"specialization"},It={class:"counselor-meta"},Mt={class:"meta-item"},Rt={class:"sessions-count"},wt={class:"meta-item"},At={class:"meta-item"},Tt={class:"price-info"},Yt={class:"price"},xt={class:"counselor-actions"},Lt={class:"counselor-details"},Ot={class:"details-main"},Pt={class:"detail-section card"},Bt={class:"detail-section card"},Ut={class:"qualification-item"},Vt={class:"qualification-item"},Ht={key:0,class:"qualification-item"},Ft={class:"details-sidebar"},Gt={class:"quick-booking card"},zt={class:"booking-form"},qt={class:"contact-info card"},Jt={class:"contact-item"},jt={class:"contact-item"},Qt={class:"contact-item"},Kt={key:1,class:"empty-state"},Xt={__name:"CounselorDetail",setup(N){const S=tt(),f=st(),g=X(),k=D(!1),l=D(null),y=D(""),b=D(""),A={ANXIETY_DISORDERS:"焦虑障碍",DEPRESSION:"抑郁症",RELATIONSHIP_COUNSELING:"情感咨询",FAMILY_THERAPY:"家庭治疗",CHILD_PSYCHOLOGY:"儿童心理",ADDICTION_COUNSELING:"成瘾咨询",TRAUMA_THERAPY:"创伤治疗",CAREER_COUNSELING:"职业咨询",STRESS_MANAGEMENT:"压力管理",GENERAL_COUNSELING:"综合咨询"},T=o=>A[o]||o,Y=o=>({PENDING:"warning",APPROVED:"success",REJECTED:"danger",SUSPENDED:"info"})[o]||"info",x=o=>({PENDING:"待审核",APPROVED:"已认证",REJECTED:"已拒绝",SUSPENDED:"已暂停"})[o]||o,L=o=>et(o).format("YYYY年MM月DD日"),O=o=>o.getTime()<Date.now()-864e5,P=()=>{const o=[];for(let t=0;t<9;t++)o.push(t);for(let t=21;t<24;t++)o.push(t);return o},B=async()=>{try{k.value=!0;const o=await pt(S.params.id);l.value=o.data||o}catch(o){console.error("Failed to fetch counselor detail:",o),v.error("获取咨询师信息失败")}finally{k.value=!1}},U=()=>{if(!g.isLoggedIn){v.warning("请先登录"),f.push("/login");return}f.push(`/booking/${S.params.id}`)},V=async()=>{if(!g.isLoggedIn){v.warning("请先登录"),f.push("/login");return}if(!y.value){v.warning("请选择预约时间");return}try{await _t.confirm("确认创建此预约吗？","确认预约",{confirmButtonText:"确认",cancelButtonText:"取消",type:"info"});const o={userId:g.userInfo.id,counselorId:l.value.id,scheduledTime:y.value,notes:b.value};await mt(o),v.success("预约创建成功"),f.push("/dashboard/appointments")}catch(o){o!=="cancel"&&(console.error("Failed to create appointment:",o),v.error("预约创建失败"))}},H=()=>{v.info("联系功能待实现")},F=async()=>{await g.logoutUser(),f.push("/")};return $(()=>{B()}),(o,t)=>{var C,I,M;const p=c("router-link"),E=c("el-button"),G=c("el-avatar"),r=c("el-icon"),z=c("el-tag"),q=c("el-date-picker"),J=c("el-input"),j=c("el-empty"),Q=Z("loading");return _(),d("div",ft,[e("nav",gt,[e("div",yt,[s(p,{to:"/",class:"nav-brand"},{default:a(()=>[...t[2]||(t[2]=[e("h2",null,"心理咨询平台",-1)])]),_:1}),e("div",Et,[s(p,{to:"/",class:"nav-link"},{default:a(()=>[...t[3]||(t[3]=[n("首页",-1)])]),_:1}),s(p,{to:"/counselors",class:"nav-link"},{default:a(()=>[...t[4]||(t[4]=[n("咨询师",-1)])]),_:1}),u(g).isLoggedIn?(_(),d(w,{key:1},[s(p,{to:"/dashboard",class:"nav-link"},{default:a(()=>[...t[7]||(t[7]=[n("个人中心",-1)])]),_:1}),s(E,{onClick:F,type:"text"},{default:a(()=>[...t[8]||(t[8]=[n("退出",-1)])]),_:1})],64)):(_(),d(w,{key:0},[s(p,{to:"/login",class:"nav-link"},{default:a(()=>[...t[5]||(t[5]=[n("登录",-1)])]),_:1}),s(p,{to:"/register",class:"nav-link nav-button"},{default:a(()=>[...t[6]||(t[6]=[n("注册",-1)])]),_:1})],64))])])]),W((_(),d("div",kt,[l.value?(_(),d("div",Dt,[e("div",bt,[e("div",ht,[s(G,{size:120,src:(C=l.value.user)==null?void 0:C.avatarUrl},{default:a(()=>{var m,R;return[n(i((R=(m=l.value.user)==null?void 0:m.username)==null?void 0:R.charAt(0)),1)]}),_:1},8,["src"]),l.value.availableForBooking?(_(),d("div",Nt,[s(r,null,{default:a(()=>[s(u(ot))]),_:1}),t[9]||(t[9]=n(" 可预约 ",-1))])):h("",!0)]),e("div",St,[e("h1",null,i(((I=l.value.user)==null?void 0:I.realName)||((M=l.value.user)==null?void 0:M.username)),1),e("p",Ct,i(T(l.value.specialization)),1),e("div",It,[e("div",Mt,[s(r,null,{default:a(()=>[s(u(at))]),_:1}),e("span",null,i(l.value.rating||"暂无评分"),1),e("span",Rt,"("+i(l.value.totalSessions)+"次咨询)",1)]),e("div",wt,[s(r,null,{default:a(()=>[s(u(lt))]),_:1}),e("span",null,i(l.value.yearsOfExperience||0)+"年经验",1)]),e("div",At,[s(r,null,{default:a(()=>[s(u(nt))]),_:1}),e("span",null,i(l.value.education||"暂无信息"),1)])]),e("div",Tt,[e("span",Yt,"¥"+i(l.value.hourlyRate),1),t[10]||(t[10]=e("span",{class:"price-unit"},"/小时",-1))])]),e("div",xt,[s(E,{type:"primary",size:"large",disabled:!l.value.availableForBooking,onClick:U},{default:a(()=>[s(r,null,{default:a(()=>[s(u(it))]),_:1}),n(" "+i(l.value.availableForBooking?"立即预约":"暂不可约"),1)]),_:1},8,["disabled"]),s(E,{size:"large",onClick:H},{default:a(()=>[s(r,null,{default:a(()=>[s(u(ut))]),_:1}),t[11]||(t[11]=n(" 联系咨询师 ",-1))]),_:1})])]),e("div",Lt,[e("div",Ot,[e("div",Pt,[t[12]||(t[12]=e("h3",null,"专业简介",-1)),e("p",null,i(l.value.professionalSummary||"暂无简介"),1)]),e("div",Bt,[t[16]||(t[16]=e("h3",null,"专业资质",-1)),e("div",Ut,[t[13]||(t[13]=e("strong",null,"执业证书编号：",-1)),e("span",null,i(l.value.licenseNumber),1)]),e("div",Vt,[t[14]||(t[14]=e("strong",null,"认证状态：",-1)),s(z,{type:Y(l.value.status)},{default:a(()=>[n(i(x(l.value.status)),1)]),_:1},8,["type"])]),l.value.verifiedAt?(_(),d("div",Ht,[t[15]||(t[15]=e("strong",null,"认证时间：",-1)),e("span",null,i(L(l.value.verifiedAt)),1)])):h("",!0)]),t[17]||(t[17]=e("div",{class:"detail-section card"},[e("h3",null,"服务时间"),e("p",null,"周一至周日 9:00-21:00"),e("p",{class:"note"},"具体时间可在预约时协商调整")],-1))]),e("div",Ft,[e("div",Gt,[t[19]||(t[19]=e("h3",null,"快速预约",-1)),e("div",zt,[s(q,{modelValue:y.value,"onUpdate:modelValue":t[0]||(t[0]=m=>y.value=m),type:"datetime",placeholder:"选择预约时间","disabled-date":O,"disabled-hours":P,format:"YYYY-MM-DD HH:mm","value-format":"YYYY-MM-DD HH:mm:ss",style:{width:"100%","margin-bottom":"15px"}},null,8,["modelValue"]),s(J,{modelValue:b.value,"onUpdate:modelValue":t[1]||(t[1]=m=>b.value=m),type:"textarea",placeholder:"请简要描述您的咨询需求...",rows:3,maxlength:"200","show-word-limit":"",style:{"margin-bottom":"15px"}},null,8,["modelValue"]),s(E,{type:"primary",disabled:!l.value.availableForBooking||!y.value,onClick:V,style:{width:"100%"}},{default:a(()=>[...t[18]||(t[18]=[n(" 确认预约 ",-1)])]),_:1},8,["disabled"])])]),e("div",qt,[t[23]||(t[23]=e("h3",null,"联系方式",-1)),e("div",Jt,[s(r,null,{default:a(()=>[s(u(rt))]),_:1}),t[20]||(t[20]=e("span",null,"平台内消息",-1))]),e("div",jt,[s(r,null,{default:a(()=>[s(u(dt))]),_:1}),t[21]||(t[21]=e("span",null,"视频咨询",-1))]),e("div",Qt,[s(r,null,{default:a(()=>[s(u(ct))]),_:1}),t[22]||(t[22]=e("span",null,"语音咨询",-1))])])])])])):k.value?h("",!0):(_(),d("div",Kt,[s(j,{description:"咨询师信息不存在"},{default:a(()=>[s(p,{to:"/counselors"},{default:a(()=>[s(E,{type:"primary"},{default:a(()=>[...t[24]||(t[24]=[n("返回咨询师列表",-1)])]),_:1})]),_:1})]),_:1})]))])),[[Q,k.value]])])}}},te=vt(Xt,[["__scopeId","data-v-b556da0b"]]);export{te as default};
