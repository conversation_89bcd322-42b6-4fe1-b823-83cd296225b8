package com.psychology.platform.repository;

import com.psychology.platform.entity.Appointment;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 预约数据访问层
 * 提供预约相关的数据库操作方法
 * 
 * <AUTHOR> Platform Team
 * @version 1.0.0
 */
@Repository
public interface AppointmentRepository extends JpaRepository<Appointment, Long> {

    /**
     * 根据用户ID查找预约
     */
    Page<Appointment> findByUserId(Long userId, Pageable pageable);

    /**
     * 根据咨询师ID查找预约
     */
    Page<Appointment> findByCounselorId(Long counselorId, Pageable pageable);

    /**
     * 根据状态查找预约
     */
    Page<Appointment> findByStatus(Appointment.AppointmentStatus status, Pageable pageable);

    /**
     * 根据用户ID和状态查找预约
     */
    Page<Appointment> findByUserIdAndStatus(Long userId, Appointment.AppointmentStatus status, Pageable pageable);

    /**
     * 根据咨询师ID和状态查找预约
     */
    Page<Appointment> findByCounselorIdAndStatus(Long counselorId, Appointment.AppointmentStatus status, Pageable pageable);

    /**
     * 查找指定时间范围内的预约
     */
    @Query("SELECT a FROM Appointment a WHERE a.scheduledTime BETWEEN :startTime AND :endTime")
    Page<Appointment> findByScheduledTimeBetween(@Param("startTime") LocalDateTime startTime, 
                                               @Param("endTime") LocalDateTime endTime, 
                                               Pageable pageable);

    /**
     * 查找咨询师在指定时间范围内的预约
     */
    @Query("SELECT a FROM Appointment a WHERE a.counselor.id = :counselorId AND a.scheduledTime BETWEEN :startTime AND :endTime")
    List<Appointment> findByCounselorIdAndScheduledTimeBetween(@Param("counselorId") Long counselorId,
                                                             @Param("startTime") LocalDateTime startTime,
                                                             @Param("endTime") LocalDateTime endTime);

    /**
     * 检查咨询师在指定时间是否有冲突的预约
     * 使用简单的时间范围重叠检查
     */
    @Query("SELECT COUNT(a) > 0 FROM Appointment a WHERE a.counselor.id = :counselorId AND " +
           "a.status IN ('CONFIRMED', 'IN_PROGRESS') AND " +
           "a.scheduledTime BETWEEN :startTime AND :endTime")
    boolean existsConflictingAppointment(@Param("counselorId") Long counselorId,
                                       @Param("startTime") LocalDateTime startTime,
                                       @Param("endTime") LocalDateTime endTime);

    /**
     * 查找需要发送提醒的预约
     */
    @Query("SELECT a FROM Appointment a WHERE a.status = 'CONFIRMED' AND a.reminderSent = false AND " +
           "a.scheduledTime BETWEEN :now AND :reminderTime")
    List<Appointment> findAppointmentsNeedingReminder(@Param("now") LocalDateTime now,
                                                     @Param("reminderTime") LocalDateTime reminderTime);

    /**
     * 查找今日预约
     */
    @Query("SELECT a FROM Appointment a WHERE FUNCTION('DATE', a.scheduledTime) = FUNCTION('DATE', :today)")
    Page<Appointment> findTodayAppointments(@Param("today") LocalDateTime today, Pageable pageable);

    /**
     * 查找用户今日预约
     */
    @Query("SELECT a FROM Appointment a WHERE a.user.id = :userId AND FUNCTION('DATE', a.scheduledTime) = FUNCTION('DATE', :today)")
    List<Appointment> findUserTodayAppointments(@Param("userId") Long userId, @Param("today") LocalDateTime today);

    /**
     * 查找咨询师今日预约
     */
    @Query("SELECT a FROM Appointment a WHERE a.counselor.id = :counselorId AND FUNCTION('DATE', a.scheduledTime) = FUNCTION('DATE', :today)")
    List<Appointment> findCounselorTodayAppointments(@Param("counselorId") Long counselorId, @Param("today") LocalDateTime today);

    /**
     * 统计用户预约总数
     */
    long countByUserId(Long userId);

    /**
     * 统计咨询师预约总数
     */
    long countByCounselorId(Long counselorId);

    /**
     * 统计各状态的预约数量
     */
    long countByStatus(Appointment.AppointmentStatus status);

    /**
     * 查找即将开始的预约
     */
    @Query("SELECT a FROM Appointment a WHERE a.status = 'CONFIRMED' AND " +
           "a.scheduledTime BETWEEN :now AND :upcomingTime ORDER BY a.scheduledTime")
    List<Appointment> findUpcomingAppointments(@Param("now") LocalDateTime now,
                                             @Param("upcomingTime") LocalDateTime upcomingTime);
}
