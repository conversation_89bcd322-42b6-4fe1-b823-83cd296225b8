<template>
  <div class="home">
    <!-- 导航栏 -->
    <nav class="navbar">
      <div class="nav-container">
        <div class="nav-brand">
          <h2>心理咨询平台</h2>
        </div>
        <div class="nav-menu">
          <router-link to="/counselors" class="nav-link">咨询师</router-link>
          <template v-if="!userStore.isLoggedIn">
            <router-link to="/login" class="nav-link">登录</router-link>
            <router-link to="/register" class="nav-link nav-button">注册</router-link>
          </template>
          <template v-else>
            <router-link to="/dashboard" class="nav-link">个人中心</router-link>
            <el-button @click="handleLogout" type="text">退出</el-button>
          </template>
        </div>
      </div>
    </nav>

    <!-- 英雄区域 -->
    <section class="hero">
      <div class="hero-content">
        <h1 class="hero-title">专业的心理健康服务</h1>
        <p class="hero-subtitle">连接您与专业心理咨询师，获得个性化的心理健康支持</p>
        <div class="hero-actions">
          <router-link to="/counselors">
            <el-button type="primary" size="large" class="hero-button">
              <el-icon><Search /></el-icon>
              寻找咨询师
            </el-button>
          </router-link>
          <router-link to="/register" v-if="!userStore.isLoggedIn">
            <el-button size="large" class="hero-button-secondary">
              立即注册
            </el-button>
          </router-link>
        </div>
      </div>
      <div class="hero-image">
        <div class="hero-card">
          <el-icon class="hero-icon"><UserFilled /></el-icon>
          <h3>专业认证</h3>
          <p>所有咨询师均经过严格认证</p>
        </div>
      </div>
    </section>

    <!-- 特色服务 -->
    <section class="features">
      <div class="content-wrapper">
        <h2 class="section-title">为什么选择我们</h2>
        <div class="features-grid">
          <div class="feature-card card">
            <el-icon class="feature-icon"><Lock /></el-icon>
            <h3>隐私保护</h3>
            <p>严格的隐私保护措施，确保您的信息安全</p>
          </div>
          <div class="feature-card card">
            <el-icon class="feature-icon"><Clock /></el-icon>
            <h3>灵活预约</h3>
            <p>24/7在线预约，随时随地获得专业帮助</p>
          </div>
          <div class="feature-card card">
            <el-icon class="feature-icon"><Star /></el-icon>
            <h3>专业团队</h3>
            <p>经验丰富的心理咨询师团队</p>
          </div>
          <div class="feature-card card">
            <el-icon class="feature-icon"><ChatDotRound /></el-icon>
            <h3>多种方式</h3>
            <p>支持视频、语音、文字等多种咨询方式</p>
          </div>
        </div>
      </div>
    </section>

    <!-- 热门咨询师 -->
    <section class="popular-counselors">
      <div class="content-wrapper">
        <h2 class="section-title">热门咨询师</h2>
        <div class="counselors-grid" v-loading="loading">
          <div 
            v-for="counselor in popularCounselors" 
            :key="counselor.id"
            class="counselor-card card"
            @click="goToCounselorDetail(counselor.id)"
          >
            <div class="counselor-avatar">
              <el-avatar :size="60" :src="counselor.user?.avatarUrl">
                {{ counselor.user?.username?.charAt(0) }}
              </el-avatar>
            </div>
            <div class="counselor-info">
              <h4>{{ counselor.user?.realName || counselor.user?.username }}</h4>
              <p class="specialization">{{ getSpecializationText(counselor.specialization) }}</p>
              <div class="counselor-meta">
                <span class="rating">
                  <el-icon><Star /></el-icon>
                  {{ counselor.rating || '暂无评分' }}
                </span>
                <span class="price">¥{{ counselor.hourlyRate }}/小时</span>
              </div>
            </div>
          </div>
        </div>
        <div class="text-center mt-30">
          <router-link to="/counselors">
            <el-button type="primary">查看更多咨询师</el-button>
          </router-link>
        </div>
      </div>
    </section>

    <!-- 页脚 -->
    <footer class="footer">
      <div class="content-wrapper">
        <div class="footer-content">
          <div class="footer-section">
            <h4>心理咨询平台</h4>
            <p>专业的线上心理健康服务平台</p>
          </div>
          <div class="footer-section">
            <h4>服务</h4>
            <ul>
              <li><a href="#">心理咨询</a></li>
              <li><a href="#">情感咨询</a></li>
              <li><a href="#">职业规划</a></li>
            </ul>
          </div>
          <div class="footer-section">
            <h4>支持</h4>
            <ul>
              <li><a href="#">帮助中心</a></li>
              <li><a href="#">联系我们</a></li>
              <li><a href="#">隐私政策</a></li>
            </ul>
          </div>
        </div>
        <div class="footer-bottom">
          <p>&copy; 2024 心理咨询平台. All rights reserved.</p>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { getPopularCounselors } from '@/api/counselor'
import { ElMessage } from 'element-plus'
import {
  Search, UserFilled, Lock, Clock, Star,
  ChatDotRound
} from '@element-plus/icons-vue'

const router = useRouter()
const userStore = useUserStore()

const loading = ref(false)
const popularCounselors = ref([])

// 专业领域映射
const specializationMap = {
  'ANXIETY_DISORDERS': '焦虑障碍',
  'DEPRESSION': '抑郁症',
  'RELATIONSHIP_COUNSELING': '情感咨询',
  'FAMILY_THERAPY': '家庭治疗',
  'CHILD_PSYCHOLOGY': '儿童心理',
  'ADDICTION_COUNSELING': '成瘾咨询',
  'TRAUMA_THERAPY': '创伤治疗',
  'CAREER_COUNSELING': '职业咨询',
  'STRESS_MANAGEMENT': '压力管理',
  'GENERAL_COUNSELING': '综合咨询'
}

const getSpecializationText = (specialization) => {
  return specializationMap[specialization] || specialization
}

const fetchPopularCounselors = async () => {
  try {
    loading.value = true
    const response = await getPopularCounselors(6)
    popularCounselors.value = response.data || response
  } catch (error) {
    console.error('Failed to fetch popular counselors:', error)
  } finally {
    loading.value = false
  }
}

const goToCounselorDetail = (id) => {
  router.push(`/counselors/${id}`)
}

const handleLogout = async () => {
  await userStore.logoutUser()
  router.push('/')
}

onMounted(() => {
  fetchPopularCounselors()
})
</script>

<style lang="scss" scoped>
.home {
  min-height: 100vh;
}

// 导航栏
.navbar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 70px;
}

.nav-brand h2 {
  color: var(--primary-color);
  font-weight: 600;
}

.nav-menu {
  display: flex;
  align-items: center;
  gap: 30px;
}

.nav-link {
  color: var(--text-regular);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
  
  &:hover {
    color: var(--primary-color);
  }
  
  &.nav-button {
    background: var(--primary-color);
    color: white;
    padding: 8px 20px;
    border-radius: 20px;
    
    &:hover {
      background: var(--primary-dark);
      color: white;
    }
  }
}

// 英雄区域
.hero {
  padding: 100px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1200px;
  margin: 0 auto;
  min-height: 500px;
}

.hero-content {
  flex: 1;
  max-width: 500px;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  color: white;
  margin-bottom: 20px;
  line-height: 1.2;
}

.hero-subtitle {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 40px;
  line-height: 1.6;
}

.hero-actions {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.hero-button {
  padding: 15px 30px;
  font-size: 1.1rem;
  border-radius: 25px;
}

.hero-button-secondary {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  padding: 13px 30px;
  font-size: 1.1rem;
  border-radius: 25px;
  
  &:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
  }
}

.hero-image {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.hero-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 40px;
  text-align: center;
  color: white;
}

.hero-icon {
  font-size: 3rem;
  margin-bottom: 20px;
  color: rgba(255, 255, 255, 0.9);
}

// 特色服务
.features {
  padding: 100px 20px;
  background: var(--bg-white);
}

.section-title {
  text-align: center;
  font-size: 2.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 60px;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
}

.feature-card {
  text-align: center;
  padding: 40px 30px;
}

.feature-icon {
  font-size: 3rem;
  color: var(--primary-color);
  margin-bottom: 20px;
}

.feature-card h3 {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 15px;
}

.feature-card p {
  color: var(--text-regular);
  line-height: 1.6;
}

// 热门咨询师
.popular-counselors {
  padding: 100px 20px;
  background: var(--bg-light);
}

.counselors-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  margin-bottom: 40px;
}

.counselor-card {
  padding: 30px;
  display: flex;
  align-items: center;
  gap: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-5px);
  }
}

.counselor-avatar {
  flex-shrink: 0;
}

.counselor-info {
  flex: 1;
}

.counselor-info h4 {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.specialization {
  color: var(--text-secondary);
  margin-bottom: 12px;
}

.counselor-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.rating {
  display: flex;
  align-items: center;
  gap: 5px;
  color: #f39c12;
  font-size: 0.9rem;
}

.price {
  color: var(--primary-color);
  font-weight: 600;
}

// 页脚
.footer {
  background: var(--text-primary);
  color: white;
  padding: 60px 20px 20px;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 40px;
  margin-bottom: 40px;
}

.footer-section h4 {
  margin-bottom: 20px;
  font-size: 1.2rem;
}

.footer-section ul {
  list-style: none;
}

.footer-section li {
  margin-bottom: 10px;
}

.footer-section a {
  color: rgba(255, 255, 255, 0.7);
  text-decoration: none;
  transition: color 0.3s ease;
  
  &:hover {
    color: white;
  }
}

.footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 20px;
  text-align: center;
  color: rgba(255, 255, 255, 0.7);
}

// 响应式设计
@media (max-width: 768px) {
  .hero {
    flex-direction: column;
    text-align: center;
    padding: 60px 20px;
  }
  
  .hero-title {
    font-size: 2.5rem;
  }
  
  .hero-actions {
    justify-content: center;
  }
  
  .nav-menu {
    gap: 15px;
  }
  
  .counselors-grid {
    grid-template-columns: 1fr;
  }
  
  .counselor-card {
    flex-direction: column;
    text-align: center;
  }
}
</style>
