<template>
  <div class="counselor-center-container">
    <div class="center-header">
      <h2>咨询师中心</h2>
      <p class="subtitle">管理您的咨询服务和客户</p>
    </div>

    <!-- 统计概览 -->
    <div class="stats-overview">
      <div class="stat-card">
        <div class="stat-icon">📊</div>
        <div class="stat-content">
          <h3>{{ stats.totalSessions }}</h3>
          <p>总咨询次数</p>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon">👥</div>
        <div class="stat-content">
          <h3>{{ stats.totalClients }}</h3>
          <p>服务客户数</p>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon">⭐</div>
        <div class="stat-content">
          <h3>{{ stats.averageRating }}</h3>
          <p>平均评分</p>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon">💰</div>
        <div class="stat-content">
          <h3>¥{{ stats.totalEarnings }}</h3>
          <p>总收入</p>
        </div>
      </div>
    </div>

    <!-- 快捷操作 -->
    <div class="quick-actions">
      <h3>快捷操作</h3>
      <div class="action-buttons">
        <button @click="setAvailability" class="action-btn">
          <span class="btn-icon">🕒</span>
          设置可预约时间
        </button>
        <button @click="updateProfile" class="action-btn">
          <span class="btn-icon">👤</span>
          更新个人资料
        </button>
        <button @click="viewEarnings" class="action-btn">
          <span class="btn-icon">📈</span>
          查看收入报告
        </button>
        <button @click="manageClients" class="action-btn">
          <span class="btn-icon">📋</span>
          客户管理
        </button>
      </div>
    </div>

    <!-- 今日预约 -->
    <div class="today-appointments">
      <h3>今日预约</h3>
      <div v-if="todayAppointments.length === 0" class="empty-state">
        <p>今天暂无预约</p>
      </div>
      <div v-else class="appointment-list">
        <div 
          v-for="appointment in todayAppointments" 
          :key="appointment.id"
          class="appointment-item"
        >
          <div class="appointment-time">
            <span class="time">{{ formatTime(appointment.scheduledTime) }}</span>
            <span class="duration">{{ appointment.duration }}分钟</span>
          </div>
          
          <div class="client-info">
            <h4>{{ appointment.client.name }}</h4>
            <p class="consultation-type">{{ getConsultationTypeText(appointment.consultationType) }}</p>
          </div>
          
          <div class="appointment-status">
            <span class="status-badge" :class="getStatusClass(appointment.status)">
              {{ getStatusText(appointment.status) }}
            </span>
          </div>
          
          <div class="appointment-actions">
            <button 
              v-if="appointment.status === 'CONFIRMED'"
              @click="startSession(appointment.id)"
              class="btn btn-primary btn-sm"
            >
              开始咨询
            </button>
            <button 
              @click="viewAppointmentDetails(appointment.id)"
              class="btn btn-outline btn-sm"
            >
              查看详情
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 最近评价 -->
    <div class="recent-reviews">
      <h3>最近评价</h3>
      <div v-if="recentReviews.length === 0" class="empty-state">
        <p>暂无评价</p>
      </div>
      <div v-else class="review-list">
        <div 
          v-for="review in recentReviews" 
          :key="review.id"
          class="review-item"
        >
          <div class="review-header">
            <div class="client-info">
              <span class="client-name">{{ review.client.name }}</span>
              <span class="review-date">{{ formatDate(review.createdAt) }}</span>
            </div>
            <div class="rating">
              <div class="stars">
                <span v-for="i in 5" :key="i" class="star" :class="{ active: i <= review.rating }">
                  ⭐
                </span>
              </div>
              <span class="rating-value">{{ review.rating }}/5</span>
            </div>
          </div>
          
          <div class="review-content">
            <p>{{ review.comment }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 收入趋势 -->
    <div class="earnings-chart">
      <h3>收入趋势</h3>
      <div class="chart-placeholder">
        <p>📈 收入图表功能开发中...</p>
      </div>
    </div>

    <!-- 咨询师状态设置 -->
    <div class="status-settings">
      <h3>状态设置</h3>
      <div class="setting-item">
        <label class="setting-label">
          <input 
            type="checkbox" 
            v-model="counselorSettings.availableForBooking"
            @change="updateAvailability"
          >
          <span class="checkmark"></span>
          接受新预约
        </label>
        <p class="setting-description">关闭后，客户将无法预约您的咨询服务</p>
      </div>
      
      <div class="setting-item">
        <label class="setting-label">
          <input 
            type="checkbox" 
            v-model="counselorSettings.autoConfirm"
            @change="updateSettings"
          >
          <span class="checkmark"></span>
          自动确认预约
        </label>
        <p class="setting-description">开启后，客户预约将自动确认，无需手动处理</p>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

export default {
  name: 'CounselorCenter',
  setup() {
    const loading = ref(false)
    const todayAppointments = ref([])
    const recentReviews = ref([])

    // 统计数据
    const stats = reactive({
      totalSessions: 0,
      totalClients: 0,
      averageRating: 0,
      totalEarnings: 0
    })

    // 咨询师设置
    const counselorSettings = reactive({
      availableForBooking: true,
      autoConfirm: false
    })

    // 模拟数据
    const mockTodayAppointments = [
      {
        id: 1,
        client: { name: '张女士' },
        scheduledTime: '2024-09-01T14:00:00',
        duration: 60,
        consultationType: 'VIDEO',
        status: 'CONFIRMED'
      },
      {
        id: 2,
        client: { name: '李先生' },
        scheduledTime: '2024-09-01T16:00:00',
        duration: 50,
        consultationType: 'VOICE',
        status: 'CONFIRMED'
      }
    ]

    const mockRecentReviews = [
      {
        id: 1,
        client: { name: '王女士' },
        rating: 5,
        comment: '非常专业，帮助很大，会继续咨询。',
        createdAt: '2024-08-30T10:00:00'
      },
      {
        id: 2,
        client: { name: '陈先生' },
        rating: 4,
        comment: '咨询师很耐心，给了很多实用的建议。',
        createdAt: '2024-08-29T15:30:00'
      }
    ]

    const mockStats = {
      totalSessions: 156,
      totalClients: 89,
      averageRating: 4.8,
      totalEarnings: 31200
    }

    // 获取状态样式类
    const getStatusClass = (status) => {
      const statusClasses = {
        'PENDING': 'status-pending',
        'CONFIRMED': 'status-confirmed',
        'IN_PROGRESS': 'status-in-progress',
        'COMPLETED': 'status-completed',
        'CANCELLED': 'status-cancelled'
      }
      return statusClasses[status] || ''
    }

    // 获取状态文本
    const getStatusText = (status) => {
      const statusTexts = {
        'PENDING': '待确认',
        'CONFIRMED': '已确认',
        'IN_PROGRESS': '进行中',
        'COMPLETED': '已完成',
        'CANCELLED': '已取消'
      }
      return statusTexts[status] || status
    }

    // 获取咨询方式文本
    const getConsultationTypeText = (type) => {
      const typeTexts = {
        'VIDEO': '视频咨询',
        'VOICE': '语音咨询',
        'TEXT': '文字咨询'
      }
      return typeTexts[type] || type
    }

    // 格式化时间
    const formatTime = (dateTime) => {
      const date = new Date(dateTime)
      return date.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit'
      })
    }

    // 格式化日期
    const formatDate = (dateTime) => {
      const date = new Date(dateTime)
      return date.toLocaleDateString('zh-CN')
    }

    // 加载数据
    const loadData = async () => {
      loading.value = true
      try {
        // TODO: 调用API获取数据
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        todayAppointments.value = mockTodayAppointments
        recentReviews.value = mockRecentReviews
        Object.assign(stats, mockStats)
      } catch (error) {
        ElMessage.error('加载数据失败')
      } finally {
        loading.value = false
      }
    }

    // 设置可预约时间
    const setAvailability = () => {
      ElMessage.info('时间设置功能开发中...')
    }

    // 更新个人资料
    const updateProfile = () => {
      ElMessage.info('资料更新功能开发中...')
    }

    // 查看收入报告
    const viewEarnings = () => {
      ElMessage.info('收入报告功能开发中...')
    }

    // 客户管理
    const manageClients = () => {
      ElMessage.info('客户管理功能开发中...')
    }

    // 开始咨询会话
    const startSession = (appointmentId) => {
      ElMessage.info('咨询会话功能开发中...')
    }

    // 查看预约详情
    const viewAppointmentDetails = (appointmentId) => {
      ElMessage.info('预约详情功能开发中...')
    }

    // 更新可预约状态
    const updateAvailability = async () => {
      try {
        // TODO: 调用API更新状态
        await new Promise(resolve => setTimeout(resolve, 500))
        
        const status = counselorSettings.availableForBooking ? '开启' : '关闭'
        ElMessage.success(`已${status}接受新预约`)
      } catch (error) {
        ElMessage.error('更新状态失败')
      }
    }

    // 更新设置
    const updateSettings = async () => {
      try {
        // TODO: 调用API更新设置
        await new Promise(resolve => setTimeout(resolve, 500))
        ElMessage.success('设置已更新')
      } catch (error) {
        ElMessage.error('更新设置失败')
      }
    }

    onMounted(() => {
      loadData()
    })

    return {
      loading,
      stats,
      todayAppointments,
      recentReviews,
      counselorSettings,
      getStatusClass,
      getStatusText,
      getConsultationTypeText,
      formatTime,
      formatDate,
      setAvailability,
      updateProfile,
      viewEarnings,
      manageClients,
      startSession,
      viewAppointmentDetails,
      updateAvailability,
      updateSettings
    }
  }
}
</script>

<style scoped>
.counselor-center-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
}

.center-header {
  margin-bottom: 32px;
}

.center-header h2 {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8px;
}

.subtitle {
  color: #6b7280;
  font-size: 14px;
}

.stats-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 32px;
}

.stat-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  font-size: 32px;
}

.stat-content h3 {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.stat-content p {
  color: #6b7280;
  font-size: 14px;
}

.quick-actions {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 32px;
}

.quick-actions h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 16px;
}

.action-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 14px;
}

.action-btn:hover {
  background: #f3f4f6;
  border-color: #d1d5db;
}

.btn-icon {
  font-size: 16px;
}

.today-appointments,
.recent-reviews,
.earnings-chart,
.status-settings {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 32px;
}

.today-appointments h3,
.recent-reviews h3,
.earnings-chart h3,
.status-settings h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 16px;
}

.empty-state {
  text-align: center;
  padding: 32px;
  color: #6b7280;
}

.appointment-list,
.review-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.appointment-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
}

.appointment-time {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 80px;
}

.time {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.duration {
  font-size: 12px;
  color: #6b7280;
}

.client-info {
  flex: 1;
}

.client-info h4 {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 2px;
}

.consultation-type {
  font-size: 12px;
  color: #6b7280;
}

.appointment-status {
  margin-right: 16px;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-pending { background: #fef3c7; color: #92400e; }
.status-confirmed { background: #d1fae5; color: #065f46; }
.status-in-progress { background: #dbeafe; color: #1e40af; }
.status-completed { background: #f3e8ff; color: #7c3aed; }
.status-cancelled { background: #fee2e2; color: #dc2626; }

.appointment-actions {
  display: flex;
  gap: 8px;
}

.btn {
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
  text-decoration: none;
  display: inline-block;
}

.btn-sm {
  padding: 4px 8px;
  font-size: 12px;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background: #2563eb;
}

.btn-outline {
  background: transparent;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn-outline:hover {
  background: #f9fafb;
}

.review-item {
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
}

.review-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.client-name {
  font-weight: 600;
  color: #1f2937;
  margin-right: 8px;
}

.review-date {
  font-size: 12px;
  color: #6b7280;
}

.rating {
  display: flex;
  align-items: center;
  gap: 8px;
}

.stars {
  display: flex;
  gap: 2px;
}

.star {
  font-size: 14px;
  opacity: 0.3;
}

.star.active {
  opacity: 1;
}

.rating-value {
  font-size: 12px;
  font-weight: 600;
  color: #7c3aed;
}

.review-content p {
  color: #6b7280;
  font-size: 14px;
  line-height: 1.5;
}

.chart-placeholder {
  text-align: center;
  padding: 48px;
  color: #6b7280;
  background: #f9fafb;
  border-radius: 8px;
}

.setting-item {
  margin-bottom: 20px;
}

.setting-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-weight: 500;
  color: #374151;
}

.setting-label input[type="checkbox"] {
  margin: 0;
}

.setting-description {
  margin-top: 4px;
  margin-left: 24px;
  color: #6b7280;
  font-size: 12px;
}
</style>
