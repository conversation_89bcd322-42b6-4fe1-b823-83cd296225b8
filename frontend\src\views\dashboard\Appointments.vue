<template>
  <div class="appointments-container">
    <div class="appointments-header">
      <h2>我的预约</h2>
      <p class="subtitle">查看和管理您的咨询预约</p>
    </div>

    <!-- 筛选器 -->
    <div class="filters">
      <div class="filter-group">
        <label>状态筛选：</label>
        <select v-model="selectedStatus" @change="filterAppointments" class="filter-select">
          <option value="">全部状态</option>
          <option value="PENDING">待确认</option>
          <option value="CONFIRMED">已确认</option>
          <option value="IN_PROGRESS">进行中</option>
          <option value="COMPLETED">已完成</option>
          <option value="CANCELLED">已取消</option>
        </select>
      </div>
      
      <div class="filter-group">
        <label>时间范围：</label>
        <select v-model="selectedTimeRange" @change="filterAppointments" class="filter-select">
          <option value="">全部时间</option>
          <option value="upcoming">即将到来</option>
          <option value="past">历史记录</option>
          <option value="today">今天</option>
          <option value="week">本周</option>
          <option value="month">本月</option>
        </select>
      </div>
    </div>

    <!-- 预约列表 -->
    <div class="appointments-list">
      <div v-if="loading" class="loading">
        <p>加载中...</p>
      </div>
      
      <div v-else-if="filteredAppointments.length === 0" class="empty-state">
        <div class="empty-icon">📅</div>
        <h3>暂无预约记录</h3>
        <p>您还没有任何咨询预约</p>
        <router-link to="/counselors" class="btn btn-primary">
          立即预约咨询
        </router-link>
      </div>
      
      <div v-else class="appointment-cards">
        <div 
          v-for="appointment in filteredAppointments" 
          :key="appointment.id"
          class="appointment-card"
          :class="getStatusClass(appointment.status)"
        >
          <!-- 预约状态标签 -->
          <div class="status-badge" :class="getStatusClass(appointment.status)">
            {{ getStatusText(appointment.status) }}
          </div>

          <!-- 咨询师信息 -->
          <div class="counselor-info">
            <img 
              :src="appointment.counselor.avatarUrl || '/default-avatar.png'" 
              :alt="appointment.counselor.name"
              class="counselor-avatar"
            >
            <div class="counselor-details">
              <h4>{{ appointment.counselor.name }}</h4>
              <p class="specialization">{{ appointment.counselor.specialization }}</p>
              <p class="rating">
                <span class="stars">⭐</span>
                {{ appointment.counselor.rating }} ({{ appointment.counselor.reviewCount }}条评价)
              </p>
            </div>
          </div>

          <!-- 预约详情 -->
          <div class="appointment-details">
            <div class="detail-item">
              <span class="label">预约时间：</span>
              <span class="value">{{ formatDateTime(appointment.scheduledTime) }}</span>
            </div>
            <div class="detail-item">
              <span class="label">咨询方式：</span>
              <span class="value">{{ getConsultationTypeText(appointment.consultationType) }}</span>
            </div>
            <div class="detail-item">
              <span class="label">费用：</span>
              <span class="value price">¥{{ appointment.totalAmount }}</span>
            </div>
            <div v-if="appointment.notes" class="detail-item">
              <span class="label">备注：</span>
              <span class="value">{{ appointment.notes }}</span>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="appointment-actions">
            <button 
              v-if="appointment.status === 'PENDING'"
              @click="cancelAppointment(appointment.id)"
              class="btn btn-danger btn-sm"
            >
              取消预约
            </button>
            
            <button 
              v-if="appointment.status === 'CONFIRMED'"
              @click="joinSession(appointment.id)"
              class="btn btn-primary btn-sm"
            >
              进入咨询
            </button>
            
            <button 
              v-if="appointment.status === 'COMPLETED' && !appointment.hasReview"
              @click="writeReview(appointment.id)"
              class="btn btn-secondary btn-sm"
            >
              写评价
            </button>
            
            <button 
              @click="viewDetails(appointment.id)"
              class="btn btn-outline btn-sm"
            >
              查看详情
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div v-if="totalPages > 1" class="pagination">
      <button 
        @click="changePage(currentPage - 1)"
        :disabled="currentPage === 1"
        class="btn btn-outline btn-sm"
      >
        上一页
      </button>
      
      <span class="page-info">
        第 {{ currentPage }} 页，共 {{ totalPages }} 页
      </span>
      
      <button 
        @click="changePage(currentPage + 1)"
        :disabled="currentPage === totalPages"
        class="btn btn-outline btn-sm"
      >
        下一页
      </button>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'

export default {
  name: 'Appointments',
  setup() {
    const router = useRouter()
    const loading = ref(false)
    const appointments = ref([])
    const selectedStatus = ref('')
    const selectedTimeRange = ref('')
    const currentPage = ref(1)
    const pageSize = ref(10)
    const totalPages = ref(1)

    // 模拟数据
    const mockAppointments = [
      {
        id: 1,
        counselor: {
          name: '张心理师',
          specialization: '焦虑症治疗',
          rating: 4.8,
          reviewCount: 156,
          avatarUrl: ''
        },
        scheduledTime: '2024-09-01T14:00:00',
        consultationType: 'VIDEO',
        totalAmount: 200,
        status: 'CONFIRMED',
        notes: '希望能帮助缓解工作压力',
        hasReview: false
      },
      {
        id: 2,
        counselor: {
          name: '李咨询师',
          specialization: '情感咨询',
          rating: 4.9,
          reviewCount: 203,
          avatarUrl: ''
        },
        scheduledTime: '2024-08-28T10:00:00',
        consultationType: 'VOICE',
        totalAmount: 180,
        status: 'COMPLETED',
        notes: '',
        hasReview: true
      }
    ]

    // 计算过滤后的预约
    const filteredAppointments = computed(() => {
      let filtered = appointments.value

      // 状态筛选
      if (selectedStatus.value) {
        filtered = filtered.filter(apt => apt.status === selectedStatus.value)
      }

      // 时间范围筛选
      if (selectedTimeRange.value) {
        const now = new Date()
        filtered = filtered.filter(apt => {
          const aptTime = new Date(apt.scheduledTime)
          
          switch (selectedTimeRange.value) {
            case 'upcoming':
              return aptTime > now
            case 'past':
              return aptTime < now
            case 'today':
              return aptTime.toDateString() === now.toDateString()
            case 'week':
              const weekStart = new Date(now.setDate(now.getDate() - now.getDay()))
              const weekEnd = new Date(weekStart)
              weekEnd.setDate(weekStart.getDate() + 6)
              return aptTime >= weekStart && aptTime <= weekEnd
            case 'month':
              return aptTime.getMonth() === now.getMonth() && aptTime.getFullYear() === now.getFullYear()
            default:
              return true
          }
        })
      }

      return filtered
    })

    // 获取状态样式类
    const getStatusClass = (status) => {
      const statusClasses = {
        'PENDING': 'status-pending',
        'CONFIRMED': 'status-confirmed',
        'IN_PROGRESS': 'status-in-progress',
        'COMPLETED': 'status-completed',
        'CANCELLED': 'status-cancelled'
      }
      return statusClasses[status] || ''
    }

    // 获取状态文本
    const getStatusText = (status) => {
      const statusTexts = {
        'PENDING': '待确认',
        'CONFIRMED': '已确认',
        'IN_PROGRESS': '进行中',
        'COMPLETED': '已完成',
        'CANCELLED': '已取消'
      }
      return statusTexts[status] || status
    }

    // 获取咨询方式文本
    const getConsultationTypeText = (type) => {
      const typeTexts = {
        'VIDEO': '视频咨询',
        'VOICE': '语音咨询',
        'TEXT': '文字咨询'
      }
      return typeTexts[type] || type
    }

    // 格式化日期时间
    const formatDateTime = (dateTime) => {
      const date = new Date(dateTime)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    }

    // 加载预约数据
    const loadAppointments = async () => {
      loading.value = true
      try {
        // TODO: 调用API获取预约数据
        await new Promise(resolve => setTimeout(resolve, 1000))
        appointments.value = mockAppointments
      } catch (error) {
        ElMessage.error('加载预约数据失败')
      } finally {
        loading.value = false
      }
    }

    // 筛选预约
    const filterAppointments = () => {
      currentPage.value = 1
      // 重新计算分页
    }

    // 取消预约
    const cancelAppointment = async (appointmentId) => {
      try {
        await ElMessageBox.confirm('确定要取消这个预约吗？', '确认取消', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        // TODO: 调用API取消预约
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        // 更新本地数据
        const appointment = appointments.value.find(apt => apt.id === appointmentId)
        if (appointment) {
          appointment.status = 'CANCELLED'
        }
        
        ElMessage.success('预约已取消')
      } catch (error) {
        if (error !== 'cancel') {
          ElMessage.error('取消预约失败')
        }
      }
    }

    // 进入咨询会话
    const joinSession = (appointmentId) => {
      // TODO: 实现进入咨询会话功能
      ElMessage.info('咨询会话功能开发中...')
    }

    // 写评价
    const writeReview = (appointmentId) => {
      // TODO: 实现评价功能
      ElMessage.info('评价功能开发中...')
    }

    // 查看详情
    const viewDetails = (appointmentId) => {
      // TODO: 实现查看详情功能
      ElMessage.info('详情页面开发中...')
    }

    // 切换页面
    const changePage = (page) => {
      if (page >= 1 && page <= totalPages.value) {
        currentPage.value = page
        loadAppointments()
      }
    }

    onMounted(() => {
      loadAppointments()
    })

    return {
      loading,
      appointments,
      filteredAppointments,
      selectedStatus,
      selectedTimeRange,
      currentPage,
      totalPages,
      getStatusClass,
      getStatusText,
      getConsultationTypeText,
      formatDateTime,
      filterAppointments,
      cancelAppointment,
      joinSession,
      writeReview,
      viewDetails,
      changePage
    }
  }
}
</script>

<style scoped>
.appointments-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
}

.appointments-header {
  margin-bottom: 32px;
}

.appointments-header h2 {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8px;
}

.subtitle {
  color: #6b7280;
  font-size: 14px;
}

.filters {
  display: flex;
  gap: 24px;
  margin-bottom: 24px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-group label {
  font-weight: 500;
  color: #374151;
  white-space: nowrap;
}

.filter-select {
  padding: 6px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
}

.loading {
  text-align: center;
  padding: 48px;
  color: #6b7280;
}

.empty-state {
  text-align: center;
  padding: 48px;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-state h3 {
  font-size: 18px;
  color: #1f2937;
  margin-bottom: 8px;
}

.empty-state p {
  color: #6b7280;
  margin-bottom: 24px;
}

.appointment-cards {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.appointment-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 20px;
  position: relative;
  transition: box-shadow 0.2s;
}

.appointment-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.status-badge {
  position: absolute;
  top: 16px;
  right: 16px;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
}

.status-pending { background: #fef3c7; color: #92400e; }
.status-confirmed { background: #d1fae5; color: #065f46; }
.status-in-progress { background: #dbeafe; color: #1e40af; }
.status-completed { background: #f3e8ff; color: #7c3aed; }
.status-cancelled { background: #fee2e2; color: #dc2626; }

.counselor-info {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
}

.counselor-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
}

.counselor-details h4 {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.specialization {
  color: #6b7280;
  font-size: 14px;
  margin-bottom: 4px;
}

.rating {
  color: #6b7280;
  font-size: 14px;
}

.stars {
  color: #fbbf24;
}

.appointment-details {
  margin-bottom: 16px;
}

.detail-item {
  display: flex;
  margin-bottom: 8px;
}

.detail-item .label {
  font-weight: 500;
  color: #374151;
  min-width: 80px;
}

.detail-item .value {
  color: #6b7280;
}

.price {
  color: #dc2626;
  font-weight: 600;
}

.appointment-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.btn {
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
  text-decoration: none;
  display: inline-block;
}

.btn-sm {
  padding: 4px 8px;
  font-size: 12px;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background: #2563eb;
}

.btn-secondary {
  background: #f3f4f6;
  color: #374151;
}

.btn-secondary:hover {
  background: #e5e7eb;
}

.btn-danger {
  background: #ef4444;
  color: white;
}

.btn-danger:hover {
  background: #dc2626;
}

.btn-outline {
  background: transparent;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn-outline:hover {
  background: #f9fafb;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  margin-top: 32px;
}

.page-info {
  color: #6b7280;
  font-size: 14px;
}
</style>
