import{c as d,a as s,n as U,A as b,i as n,Z as t,_ as V,u as F,r as v,z as p,o as h,E as l,m}from"./index-D4DFMkO1.js";import{_ as x}from"./_plugin-vue_export-helper-DlAUqK2U.js";const D={name:"Profile",setup(){const w=F(),o=v(!1),f=v(!1),r=p({username:"",email:"",realName:"",phone:"",gender:"",birthDate:"",bio:""}),i=p({currentPassword:"",newPassword:"",confirmPassword:""}),u=p({username:"",email:"",avatarUrl:""}),a=()=>{const e=w.user;e&&(Object.assign(r,{username:e.username||"",email:e.email||"",realName:e.realName||"",phone:e.phone||"",gender:e.gender||"",birthDate:e.birthDate||"",bio:e.bio||""}),Object.assign(u,{username:e.username||"",email:e.email||"",avatarUrl:e.avatarUrl||""}))},g=async()=>{o.value=!0;try{await new Promise(e=>setTimeout(e,1e3)),l.success("个人信息更新成功")}catch{l.error("更新失败，请重试")}finally{o.value=!1}},c=()=>{a()},P=()=>{l.info("头像上传功能开发中...")},y=async()=>{if(i.newPassword!==i.confirmPassword){l.error("两次输入的密码不一致");return}f.value=!0;try{await new Promise(e=>setTimeout(e,1e3)),l.success("密码修改成功"),Object.assign(i,{currentPassword:"",newPassword:"",confirmPassword:""})}catch{l.error("密码修改失败，请重试")}finally{f.value=!1}};return h(()=>{a()}),{form:r,passwordForm:i,userInfo:u,loading:o,passwordLoading:f,handleSubmit:g,resetForm:c,handleAvatarUpload:P,handlePasswordChange:y}}},N={class:"profile-container"},k={class:"profile-content"},S={class:"avatar-section"},E={class:"avatar-wrapper"},M=["src","alt"],A={class:"form-section"},_={class:"form-group"},C={class:"form-group"},L={class:"form-group"},T={class:"form-group"},I={class:"form-group"},O={class:"form-group"},j={class:"form-group"},B={class:"form-actions"},z=["disabled"],H={key:0},R={key:1},Z={class:"password-section"},q={class:"form-group"},G={class:"form-group"},J={class:"form-group"},K={class:"form-actions"},Q=["disabled"],W={key:0},X={key:1};function Y(w,o,f,r,i,u){return m(),d("div",N,[o[27]||(o[27]=s("div",{class:"profile-header"},[s("h2",null,"个人信息"),s("p",{class:"subtitle"},"管理您的个人资料和账户设置")],-1)),s("div",k,[s("div",S,[s("div",E,[s("img",{src:r.userInfo.avatarUrl||"/default-avatar.png",alt:r.userInfo.username,class:"avatar"},null,8,M),s("button",{class:"avatar-upload-btn",onClick:o[0]||(o[0]=(...a)=>r.handleAvatarUpload&&r.handleAvatarUpload(...a))},[...o[14]||(o[14]=[s("i",{class:"icon-camera"},null,-1),U(" 更换头像 ",-1)])])])]),s("div",A,[s("form",{onSubmit:o[9]||(o[9]=b((...a)=>r.handleSubmit&&r.handleSubmit(...a),["prevent"])),class:"profile-form"},[s("div",_,[o[15]||(o[15]=s("label",{for:"username"},"用户名",-1)),n(s("input",{id:"username","onUpdate:modelValue":o[1]||(o[1]=a=>r.form.username=a),type:"text",class:"form-input",readonly:""},null,512),[[t,r.form.username]])]),s("div",C,[o[16]||(o[16]=s("label",{for:"email"},"邮箱",-1)),n(s("input",{id:"email","onUpdate:modelValue":o[2]||(o[2]=a=>r.form.email=a),type:"email",class:"form-input",readonly:""},null,512),[[t,r.form.email]])]),s("div",L,[o[17]||(o[17]=s("label",{for:"realName"},"真实姓名",-1)),n(s("input",{id:"realName","onUpdate:modelValue":o[3]||(o[3]=a=>r.form.realName=a),type:"text",class:"form-input",placeholder:"请输入真实姓名"},null,512),[[t,r.form.realName]])]),s("div",T,[o[18]||(o[18]=s("label",{for:"phone"},"手机号",-1)),n(s("input",{id:"phone","onUpdate:modelValue":o[4]||(o[4]=a=>r.form.phone=a),type:"tel",class:"form-input",placeholder:"请输入手机号"},null,512),[[t,r.form.phone]])]),s("div",I,[o[20]||(o[20]=s("label",{for:"gender"},"性别",-1)),n(s("select",{id:"gender","onUpdate:modelValue":o[5]||(o[5]=a=>r.form.gender=a),class:"form-select"},[...o[19]||(o[19]=[s("option",{value:""},"请选择性别",-1),s("option",{value:"MALE"},"男",-1),s("option",{value:"FEMALE"},"女",-1),s("option",{value:"OTHER"},"其他",-1)])],512),[[V,r.form.gender]])]),s("div",O,[o[21]||(o[21]=s("label",{for:"birthDate"},"出生日期",-1)),n(s("input",{id:"birthDate","onUpdate:modelValue":o[6]||(o[6]=a=>r.form.birthDate=a),type:"date",class:"form-input"},null,512),[[t,r.form.birthDate]])]),s("div",j,[o[22]||(o[22]=s("label",{for:"bio"},"个人简介",-1)),n(s("textarea",{id:"bio","onUpdate:modelValue":o[7]||(o[7]=a=>r.form.bio=a),class:"form-textarea",placeholder:"介绍一下自己...",rows:"4"},null,512),[[t,r.form.bio]])]),s("div",B,[s("button",{type:"submit",class:"btn btn-primary",disabled:r.loading},[r.loading?(m(),d("span",H,"保存中...")):(m(),d("span",R,"保存更改"))],8,z),s("button",{type:"button",class:"btn btn-secondary",onClick:o[8]||(o[8]=(...a)=>r.resetForm&&r.resetForm(...a))}," 重置 ")])],32)]),s("div",Z,[o[26]||(o[26]=s("h3",null,"修改密码",-1)),s("form",{onSubmit:o[13]||(o[13]=b((...a)=>r.handlePasswordChange&&r.handlePasswordChange(...a),["prevent"])),class:"password-form"},[s("div",q,[o[23]||(o[23]=s("label",{for:"currentPassword"},"当前密码",-1)),n(s("input",{id:"currentPassword","onUpdate:modelValue":o[10]||(o[10]=a=>r.passwordForm.currentPassword=a),type:"password",class:"form-input",placeholder:"请输入当前密码"},null,512),[[t,r.passwordForm.currentPassword]])]),s("div",G,[o[24]||(o[24]=s("label",{for:"newPassword"},"新密码",-1)),n(s("input",{id:"newPassword","onUpdate:modelValue":o[11]||(o[11]=a=>r.passwordForm.newPassword=a),type:"password",class:"form-input",placeholder:"请输入新密码"},null,512),[[t,r.passwordForm.newPassword]])]),s("div",J,[o[25]||(o[25]=s("label",{for:"confirmPassword"},"确认新密码",-1)),n(s("input",{id:"confirmPassword","onUpdate:modelValue":o[12]||(o[12]=a=>r.passwordForm.confirmPassword=a),type:"password",class:"form-input",placeholder:"请再次输入新密码"},null,512),[[t,r.passwordForm.confirmPassword]])]),s("div",K,[s("button",{type:"submit",class:"btn btn-primary",disabled:r.passwordLoading},[r.passwordLoading?(m(),d("span",W,"修改中...")):(m(),d("span",X,"修改密码"))],8,Q)])],32)])])])}const so=x(D,[["render",Y],["__scopeId","data-v-f65c9409"]]);export{so as default};
