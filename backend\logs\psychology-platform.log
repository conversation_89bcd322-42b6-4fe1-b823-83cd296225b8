2025-08-31T21:02:15.023+08:00  INFO 21112 --- [psychology-platform] [restartedMain] c.p.p.PsychologyPlatformApplication      : Starting PsychologyPlatformApplication using Java 21.0.8 with PID 21112 (C:\Users\<USER>\Desktop\a\backend\target\classes started by 21653 in C:\Users\<USER>\Desktop\a\backend)
2025-08-31T21:02:15.026+08:00  INFO 21112 --- [psychology-platform] [restartedMain] c.p.p.PsychologyPlatformApplication      : No active profile set, falling back to 1 default profile: "default"
2025-08-31T21:02:15.104+08:00  INFO 21112 --- [psychology-platform] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-31T21:02:15.105+08:00  INFO 21112 --- [psychology-platform] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-31T21:02:16.153+08:00  INFO 21112 --- [psychology-platform] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-31T21:02:16.243+08:00  INFO 21112 --- [psychology-platform] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 77 ms. Found 4 JPA repository interfaces.
2025-08-31T21:02:18.166+08:00  INFO 21112 --- [psychology-platform] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-08-31T21:02:18.179+08:00  INFO 21112 --- [psychology-platform] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-31T21:02:18.179+08:00  INFO 21112 --- [psychology-platform] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-08-31T21:02:18.244+08:00  INFO 21112 --- [psychology-platform] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring embedded WebApplicationContext
2025-08-31T21:02:18.245+08:00  INFO 21112 --- [psychology-platform] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 3139 ms
2025-08-31T21:02:18.553+08:00  INFO 21112 --- [psychology-platform] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-31T21:02:18.652+08:00  INFO 21112 --- [psychology-platform] [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.3.1.Final
2025-08-31T21:02:18.710+08:00  INFO 21112 --- [psychology-platform] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-31T21:02:18.719+08:00 ERROR 21112 --- [psychology-platform] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Failed to initialize JPA EntityManagerFactory: Unable to resolve name [org.hibernate.boot.model.naming.SnakeCasePhysicalNamingStrategy] as strategy [org.hibernate.boot.model.naming.PhysicalNamingStrategy]
2025-08-31T21:02:18.720+08:00 ERROR 21112 --- [psychology-platform] [restartedMain] o.s.b.web.embedded.tomcat.TomcatStarter  : Error starting Tomcat context. Exception: org.springframework.beans.factory.UnsatisfiedDependencyException. Message: Error creating bean with name 'securityConfig': Unsatisfied dependency expressed through field 'userDetailsService': Error creating bean with name 'userDetailsServiceImpl': Unsatisfied dependency expressed through field 'userRepository': Error creating bean with name 'userRepository' defined in com.psychology.platform.repository.UserRepository defined in @EnableJpaRepositories declared on DatabaseConfig: Cannot resolve reference to bean 'jpaSharedEM_entityManagerFactory' while setting bean property 'entityManager'
2025-08-31T21:02:18.751+08:00  INFO 21112 --- [psychology-platform] [restartedMain] o.apache.catalina.core.StandardService   : Stopping service [Tomcat]
2025-08-31T21:02:18.757+08:00  WARN 21112 --- [psychology-platform] [restartedMain] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Unable to start web server
2025-08-31T21:02:18.773+08:00  INFO 21112 --- [psychology-platform] [restartedMain] .s.b.a.l.ConditionEvaluationReportLogger : 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-31T21:02:18.810+08:00 ERROR 21112 --- [psychology-platform] [restartedMain] o.s.boot.SpringApplication               : Application run failed

org.springframework.context.ApplicationContextException: Unable to start web server
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:165) ~[spring-boot-3.2.0.jar:3.2.0]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:610) ~[spring-context-6.1.1.jar:6.1.1]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146) ~[spring-boot-3.2.0.jar:3.2.0]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:753) ~[spring-boot-3.2.0.jar:3.2.0]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:455) ~[spring-boot-3.2.0.jar:3.2.0]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:323) ~[spring-boot-3.2.0.jar:3.2.0]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1342) ~[spring-boot-3.2.0.jar:3.2.0]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1331) ~[spring-boot-3.2.0.jar:3.2.0]
	at com.psychology.platform.PsychologyPlatformApplication.main(PsychologyPlatformApplication.java:21) ~[classes/:na]
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:580) ~[na:na]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) ~[spring-boot-devtools-3.2.0.jar:3.2.0]
Caused by: org.springframework.boot.web.server.WebServerException: Unable to start embedded Tomcat
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize(TomcatWebServer.java:142) ~[spring-boot-3.2.0.jar:3.2.0]
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.<init>(TomcatWebServer.java:104) ~[spring-boot-3.2.0.jar:3.2.0]
	at org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory.getTomcatWebServer(TomcatServletWebServerFactory.java:501) ~[spring-boot-3.2.0.jar:3.2.0]
	at org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory.getWebServer(TomcatServletWebServerFactory.java:218) ~[spring-boot-3.2.0.jar:3.2.0]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.createWebServer(ServletWebServerApplicationContext.java:188) ~[spring-boot-3.2.0.jar:3.2.0]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:162) ~[spring-boot-3.2.0.jar:3.2.0]
	... 11 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'securityConfig': Unsatisfied dependency expressed through field 'userDetailsService': Error creating bean with name 'userDetailsServiceImpl': Unsatisfied dependency expressed through field 'userRepository': Error creating bean with name 'userRepository' defined in com.psychology.platform.repository.UserRepository defined in @EnableJpaRepositories declared on DatabaseConfig: Cannot resolve reference to bean 'jpaSharedEM_entityManagerFactory' while setting bean property 'entityManager'
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:772) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:752) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:493) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1420) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:600) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:413) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1336) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1166) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:204) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:210) ~[spring-boot-3.2.0.jar:3.2.0]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addAsRegistrationBean(ServletContextInitializerBeans.java:173) ~[spring-boot-3.2.0.jar:3.2.0]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addAsRegistrationBean(ServletContextInitializerBeans.java:168) ~[spring-boot-3.2.0.jar:3.2.0]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addAdaptableBeans(ServletContextInitializerBeans.java:153) ~[spring-boot-3.2.0.jar:3.2.0]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.<init>(ServletContextInitializerBeans.java:86) ~[spring-boot-3.2.0.jar:3.2.0]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.getServletContextInitializerBeans(ServletWebServerApplicationContext.java:266) ~[spring-boot-3.2.0.jar:3.2.0]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.selfInitialize(ServletWebServerApplicationContext.java:240) ~[spring-boot-3.2.0.jar:3.2.0]
	at org.springframework.boot.web.embedded.tomcat.TomcatStarter.onStartup(TomcatStarter.java:52) ~[spring-boot-3.2.0.jar:3.2.0]
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:4850) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:171) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1332) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1322) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317) ~[na:na]
	at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:145) ~[na:na]
	at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:866) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:845) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:171) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1332) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1322) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317) ~[na:na]
	at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:145) ~[na:na]
	at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:866) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:240) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:171) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.core.StandardService.startInternal(StandardService.java:433) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:171) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:917) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:171) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.startup.Tomcat.start(Tomcat.java:488) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize(TomcatWebServer.java:123) ~[spring-boot-3.2.0.jar:3.2.0]
	... 16 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'userDetailsServiceImpl': Unsatisfied dependency expressed through field 'userRepository': Error creating bean with name 'userRepository' defined in com.psychology.platform.repository.UserRepository defined in @EnableJpaRepositories declared on DatabaseConfig: Cannot resolve reference to bean 'jpaSharedEM_entityManagerFactory' while setting bean property 'entityManager'
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:772) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:752) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:493) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1420) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:600) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1441) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1348) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:769) ~[spring-beans-6.1.1.jar:6.1.1]
	... 67 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'userRepository' defined in com.psychology.platform.repository.UserRepository defined in @EnableJpaRepositories declared on DatabaseConfig: Cannot resolve reference to bean 'jpaSharedEM_entityManagerFactory' while setting bean property 'entityManager'
	at org.springframework.beans.factory.support.BeanDefinitionValueResolver.resolveReference(BeanDefinitionValueResolver.java:377) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.BeanDefinitionValueResolver.resolveValueIfNecessary(BeanDefinitionValueResolver.java:135) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyPropertyValues(AbstractAutowireCapableBeanFactory.java:1686) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1435) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:600) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1441) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1348) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:769) ~[spring-beans-6.1.1.jar:6.1.1]
	... 81 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'jpaSharedEM_entityManagerFactory': Cannot resolve reference to bean 'entityManagerFactory' while setting constructor argument
	at org.springframework.beans.factory.support.BeanDefinitionValueResolver.resolveReference(BeanDefinitionValueResolver.java:377) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.BeanDefinitionValueResolver.resolveValueIfNecessary(BeanDefinitionValueResolver.java:135) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.ConstructorResolver.resolveConstructorArguments(ConstructorResolver.java:689) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:513) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1336) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1166) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.BeanDefinitionValueResolver.resolveReference(BeanDefinitionValueResolver.java:365) ~[spring-beans-6.1.1.jar:6.1.1]
	... 94 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'entityManagerFactory' defined in class path resource [org/springframework/boot/autoconfigure/orm/jpa/HibernateJpaConfiguration.class]: Unable to resolve name [org.hibernate.boot.model.naming.SnakeCasePhysicalNamingStrategy] as strategy [org.hibernate.boot.model.naming.PhysicalNamingStrategy]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1775) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:601) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.BeanDefinitionValueResolver.resolveReference(BeanDefinitionValueResolver.java:365) ~[spring-beans-6.1.1.jar:6.1.1]
	... 106 common frames omitted
Caused by: org.hibernate.boot.registry.selector.spi.StrategySelectionException: Unable to resolve name [org.hibernate.boot.model.naming.SnakeCasePhysicalNamingStrategy] as strategy [org.hibernate.boot.model.naming.PhysicalNamingStrategy]
	at org.hibernate.boot.registry.selector.internal.StrategySelectorImpl.selectStrategyImplementor(StrategySelectorImpl.java:154) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.boot.registry.selector.internal.StrategySelectorImpl.resolveStrategy(StrategySelectorImpl.java:236) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.boot.registry.selector.internal.StrategySelectorImpl.resolveDefaultableStrategy(StrategySelectorImpl.java:180) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.boot.registry.selector.internal.StrategySelectorImpl.resolveDefaultableStrategy(StrategySelectorImpl.java:167) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.boot.internal.MetadataBuilderImpl$MetadataBuildingOptionsImpl.<init>(MetadataBuilderImpl.java:740) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.boot.internal.MetadataBuilderImpl.<init>(MetadataBuilderImpl.java:140) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.boot.MetadataSources.getMetadataBuilder(MetadataSources.java:164) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.<init>(EntityManagerFactoryBuilderImpl.java:277) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.<init>(EntityManagerFactoryBuilderImpl.java:198) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.springframework.orm.jpa.vendor.SpringHibernateJpaPersistenceProvider.createContainerEntityManagerFactory(SpringHibernateJpaPersistenceProvider.java:63) ~[spring-orm-6.1.1.jar:6.1.1]
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.createNativeEntityManagerFactory(LocalContainerEntityManagerFactoryBean.java:376) ~[spring-orm-6.1.1.jar:6.1.1]
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.buildNativeEntityManagerFactory(AbstractEntityManagerFactoryBean.java:409) ~[spring-orm-6.1.1.jar:6.1.1]
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.afterPropertiesSet(AbstractEntityManagerFactoryBean.java:396) ~[spring-orm-6.1.1.jar:6.1.1]
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.afterPropertiesSet(LocalContainerEntityManagerFactoryBean.java:352) ~[spring-orm-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1822) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1771) ~[spring-beans-6.1.1.jar:6.1.1]
	... 113 common frames omitted
Caused by: org.hibernate.boot.registry.classloading.spi.ClassLoadingException: Unable to load class [org.hibernate.boot.model.naming.SnakeCasePhysicalNamingStrategy]
	at org.hibernate.boot.registry.classloading.internal.ClassLoaderServiceImpl.classForName(ClassLoaderServiceImpl.java:126) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.boot.registry.selector.internal.StrategySelectorImpl.selectStrategyImplementor(StrategySelectorImpl.java:150) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	... 128 common frames omitted
Caused by: java.lang.ClassNotFoundException: Could not load requested class : org.hibernate.boot.model.naming.SnakeCasePhysicalNamingStrategy
	at org.hibernate.boot.registry.classloading.internal.AggregatedClassLoader.findClass(AggregatedClassLoader.java:216) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:593) ~[na:na]
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526) ~[na:na]
	at java.base/java.lang.Class.forName0(Native Method) ~[na:na]
	at java.base/java.lang.Class.forName(Class.java:534) ~[na:na]
	at java.base/java.lang.Class.forName(Class.java:513) ~[na:na]
	at org.hibernate.boot.registry.classloading.internal.ClassLoaderServiceImpl.classForName(ClassLoaderServiceImpl.java:123) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	... 129 common frames omitted
Caused by: java.lang.Throwable: null
	at org.hibernate.boot.registry.classloading.internal.AggregatedClassLoader.findClass(AggregatedClassLoader.java:209) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	... 135 common frames omitted
	Suppressed: java.lang.ClassNotFoundException: org.hibernate.boot.model.naming.SnakeCasePhysicalNamingStrategy
		at org.springframework.boot.web.embedded.tomcat.TomcatEmbeddedWebappClassLoader.loadClass(TomcatEmbeddedWebappClassLoader.java:72) ~[spring-boot-3.2.0.jar:3.2.0]
		at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1165) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
		at org.hibernate.boot.registry.classloading.internal.AggregatedClassLoader.findClass(AggregatedClassLoader.java:206) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
		... 135 common frames omitted
	Suppressed: java.lang.ClassNotFoundException: org.hibernate.boot.model.naming.SnakeCasePhysicalNamingStrategy
		at java.base/jdk.internal.loader.BuiltinClassLoader.loadClass(BuiltinClassLoader.java:641) ~[na:na]
		at java.base/jdk.internal.loader.ClassLoaders$AppClassLoader.loadClass(ClassLoaders.java:188) ~[na:na]
		at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526) ~[na:na]
		at org.hibernate.boot.registry.classloading.internal.AggregatedClassLoader.findClass(AggregatedClassLoader.java:206) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
		... 135 common frames omitted
	Suppressed: java.lang.ClassNotFoundException: org.hibernate.boot.model.naming.SnakeCasePhysicalNamingStrategy
		at org.springframework.boot.web.embedded.tomcat.TomcatEmbeddedWebappClassLoader.loadClass(TomcatEmbeddedWebappClassLoader.java:72) ~[spring-boot-3.2.0.jar:3.2.0]
		at org.apache.catalina.loader.WebappClassLoaderBase.loadClass(WebappClassLoaderBase.java:1165) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
		at org.hibernate.boot.registry.classloading.internal.AggregatedClassLoader.findClass(AggregatedClassLoader.java:206) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
		... 135 common frames omitted
	Suppressed: java.lang.ClassNotFoundException: org.hibernate.boot.model.naming.SnakeCasePhysicalNamingStrategy
		at java.base/jdk.internal.loader.BuiltinClassLoader.loadClass(BuiltinClassLoader.java:641) ~[na:na]
		at java.base/jdk.internal.loader.ClassLoaders$AppClassLoader.loadClass(ClassLoaders.java:188) ~[na:na]
		at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526) ~[na:na]
		at org.hibernate.boot.registry.classloading.internal.AggregatedClassLoader.findClass(AggregatedClassLoader.java:206) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
		... 135 common frames omitted

2025-08-31T21:35:22.855+08:00  INFO 6896 --- [psychology-platform] [restartedMain] c.p.p.PsychologyPlatformApplication      : Starting PsychologyPlatformApplication using Java 21.0.8 with PID 6896 (C:\Users\<USER>\Desktop\a\backend\target\classes started by 21653 in C:\Users\<USER>\Desktop\a\backend)
2025-08-31T21:35:22.859+08:00  INFO 6896 --- [psychology-platform] [restartedMain] c.p.p.PsychologyPlatformApplication      : No active profile set, falling back to 1 default profile: "default"
2025-08-31T21:35:22.936+08:00  INFO 6896 --- [psychology-platform] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-31T21:35:22.936+08:00  INFO 6896 --- [psychology-platform] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-31T21:35:23.586+08:00  INFO 6896 --- [psychology-platform] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-31T21:35:23.633+08:00  INFO 6896 --- [psychology-platform] [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 40 ms. Found 4 JPA repository interfaces.
2025-08-31T21:35:24.516+08:00  INFO 6896 --- [psychology-platform] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-08-31T21:35:24.528+08:00  INFO 6896 --- [psychology-platform] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-31T21:35:24.528+08:00  INFO 6896 --- [psychology-platform] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-08-31T21:35:24.575+08:00  INFO 6896 --- [psychology-platform] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring embedded WebApplicationContext
2025-08-31T21:35:24.575+08:00  INFO 6896 --- [psychology-platform] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1637 ms
2025-08-31T21:35:24.721+08:00  INFO 6896 --- [psychology-platform] [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-31T21:35:24.805+08:00  INFO 6896 --- [psychology-platform] [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.3.1.Final
2025-08-31T21:35:24.842+08:00  INFO 6896 --- [psychology-platform] [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-31T21:35:25.092+08:00  INFO 6896 --- [psychology-platform] [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-31T21:35:25.117+08:00  INFO 6896 --- [psychology-platform] [restartedMain] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Starting...
2025-08-31T21:35:25.271+08:00  INFO 6896 --- [psychology-platform] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : PsychologyPlatformHikariCP - Added connection com.mysql.cj.jdbc.ConnectionImpl@78a2344a
2025-08-31T21:35:25.272+08:00  INFO 6896 --- [psychology-platform] [restartedMain] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Start completed.
2025-08-31T21:35:25.326+08:00  WARN 6896 --- [psychology-platform] [restartedMain] org.hibernate.orm.deprecation            : HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-31T21:35:26.326+08:00  INFO 6896 --- [psychology-platform] [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-31T21:35:26.330+08:00  INFO 6896 --- [psychology-platform] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-31T21:35:26.629+08:00  INFO 6896 --- [psychology-platform] [restartedMain] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-08-31T21:35:27.195+08:00  WARN 6896 --- [psychology-platform] [restartedMain] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'appointmentController': Unsatisfied dependency expressed through field 'appointmentService': Error creating bean with name 'appointmentServiceImpl': Unsatisfied dependency expressed through field 'appointmentRepository': Error creating bean with name 'appointmentRepository' defined in com.psychology.platform.repository.AppointmentRepository defined in @EnableJpaRepositories declared on DatabaseConfig: Could not create query for public abstract java.util.List com.psychology.platform.repository.AppointmentRepository.findUserTodayAppointments(java.lang.Long,java.time.LocalDateTime); Reason: Validation failed for query for method public abstract java.util.List com.psychology.platform.repository.AppointmentRepository.findUserTodayAppointments(java.lang.Long,java.time.LocalDateTime)
2025-08-31T21:35:27.195+08:00  INFO 6896 --- [psychology-platform] [restartedMain] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-31T21:35:27.197+08:00  INFO 6896 --- [psychology-platform] [restartedMain] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Shutdown initiated...
2025-08-31T21:35:27.205+08:00  INFO 6896 --- [psychology-platform] [restartedMain] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Shutdown completed.
2025-08-31T21:35:27.206+08:00  INFO 6896 --- [psychology-platform] [restartedMain] o.apache.catalina.core.StandardService   : Stopping service [Tomcat]
2025-08-31T21:35:27.216+08:00  INFO 6896 --- [psychology-platform] [restartedMain] .s.b.a.l.ConditionEvaluationReportLogger : 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-31T21:35:27.230+08:00 ERROR 6896 --- [psychology-platform] [restartedMain] o.s.boot.SpringApplication               : Application run failed

org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'appointmentController': Unsatisfied dependency expressed through field 'appointmentService': Error creating bean with name 'appointmentServiceImpl': Unsatisfied dependency expressed through field 'appointmentRepository': Error creating bean with name 'appointmentRepository' defined in com.psychology.platform.repository.AppointmentRepository defined in @EnableJpaRepositories declared on DatabaseConfig: Could not create query for public abstract java.util.List com.psychology.platform.repository.AppointmentRepository.findUserTodayAppointments(java.lang.Long,java.time.LocalDateTime); Reason: Validation failed for query for method public abstract java.util.List com.psychology.platform.repository.AppointmentRepository.findUserTodayAppointments(java.lang.Long,java.time.LocalDateTime)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:772) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:752) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:493) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1420) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:600) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:973) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:946) ~[spring-context-6.1.1.jar:6.1.1]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:616) ~[spring-context-6.1.1.jar:6.1.1]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146) ~[spring-boot-3.2.0.jar:3.2.0]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:753) ~[spring-boot-3.2.0.jar:3.2.0]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:455) ~[spring-boot-3.2.0.jar:3.2.0]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:323) ~[spring-boot-3.2.0.jar:3.2.0]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1342) ~[spring-boot-3.2.0.jar:3.2.0]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1331) ~[spring-boot-3.2.0.jar:3.2.0]
	at com.psychology.platform.PsychologyPlatformApplication.main(PsychologyPlatformApplication.java:21) ~[classes/:na]
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:580) ~[na:na]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) ~[spring-boot-devtools-3.2.0.jar:3.2.0]
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'appointmentServiceImpl': Unsatisfied dependency expressed through field 'appointmentRepository': Error creating bean with name 'appointmentRepository' defined in com.psychology.platform.repository.AppointmentRepository defined in @EnableJpaRepositories declared on DatabaseConfig: Could not create query for public abstract java.util.List com.psychology.platform.repository.AppointmentRepository.findUserTodayAppointments(java.lang.Long,java.time.LocalDateTime); Reason: Validation failed for query for method public abstract java.util.List com.psychology.platform.repository.AppointmentRepository.findUserTodayAppointments(java.lang.Long,java.time.LocalDateTime)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:772) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:752) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:493) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1420) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:600) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1441) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1348) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:769) ~[spring-beans-6.1.1.jar:6.1.1]
	... 23 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'appointmentRepository' defined in com.psychology.platform.repository.AppointmentRepository defined in @EnableJpaRepositories declared on DatabaseConfig: Could not create query for public abstract java.util.List com.psychology.platform.repository.AppointmentRepository.findUserTodayAppointments(java.lang.Long,java.time.LocalDateTime); Reason: Validation failed for query for method public abstract java.util.List com.psychology.platform.repository.AppointmentRepository.findUserTodayAppointments(java.lang.Long,java.time.LocalDateTime)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1775) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:601) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1441) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1348) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:769) ~[spring-beans-6.1.1.jar:6.1.1]
	... 37 common frames omitted
Caused by: org.springframework.data.repository.query.QueryCreationException: Could not create query for public abstract java.util.List com.psychology.platform.repository.AppointmentRepository.findUserTodayAppointments(java.lang.Long,java.time.LocalDateTime); Reason: Validation failed for query for method public abstract java.util.List com.psychology.platform.repository.AppointmentRepository.findUserTodayAppointments(java.lang.Long,java.time.LocalDateTime)
	at org.springframework.data.repository.query.QueryCreationException.create(QueryCreationException.java:101) ~[spring-data-commons-3.2.0.jar:3.2.0]
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:115) ~[spring-data-commons-3.2.0.jar:3.2.0]
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.mapMethodsToQuery(QueryExecutorMethodInterceptor.java:99) ~[spring-data-commons-3.2.0.jar:3.2.0]
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lambda$new$0(QueryExecutorMethodInterceptor.java:88) ~[spring-data-commons-3.2.0.jar:3.2.0]
	at java.base/java.util.Optional.map(Optional.java:260) ~[na:na]
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.<init>(QueryExecutorMethodInterceptor.java:88) ~[spring-data-commons-3.2.0.jar:3.2.0]
	at org.springframework.data.repository.core.support.RepositoryFactorySupport.getRepository(RepositoryFactorySupport.java:357) ~[spring-data-commons-3.2.0.jar:3.2.0]
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.lambda$afterPropertiesSet$5(RepositoryFactoryBeanSupport.java:279) ~[spring-data-commons-3.2.0.jar:3.2.0]
	at org.springframework.data.util.Lazy.getNullable(Lazy.java:135) ~[spring-data-commons-3.2.0.jar:3.2.0]
	at org.springframework.data.util.Lazy.get(Lazy.java:113) ~[spring-data-commons-3.2.0.jar:3.2.0]
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.afterPropertiesSet(RepositoryFactoryBeanSupport.java:285) ~[spring-data-commons-3.2.0.jar:3.2.0]
	at org.springframework.data.jpa.repository.support.JpaRepositoryFactoryBean.afterPropertiesSet(JpaRepositoryFactoryBean.java:132) ~[spring-data-jpa-3.2.0.jar:3.2.0]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1822) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1771) ~[spring-beans-6.1.1.jar:6.1.1]
	... 47 common frames omitted
Caused by: java.lang.IllegalArgumentException: Validation failed for query for method public abstract java.util.List com.psychology.platform.repository.AppointmentRepository.findUserTodayAppointments(java.lang.Long,java.time.LocalDateTime)
	at org.springframework.data.jpa.repository.query.SimpleJpaQuery.validateQuery(SimpleJpaQuery.java:100) ~[spring-data-jpa-3.2.0.jar:3.2.0]
	at org.springframework.data.jpa.repository.query.SimpleJpaQuery.<init>(SimpleJpaQuery.java:70) ~[spring-data-jpa-3.2.0.jar:3.2.0]
	at org.springframework.data.jpa.repository.query.JpaQueryFactory.fromMethodWithQueryString(JpaQueryFactory.java:60) ~[spring-data-jpa-3.2.0.jar:3.2.0]
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$DeclaredQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:170) ~[spring-data-jpa-3.2.0.jar:3.2.0]
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$CreateIfNotFoundQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:252) ~[spring-data-jpa-3.2.0.jar:3.2.0]
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$AbstractQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:95) ~[spring-data-jpa-3.2.0.jar:3.2.0]
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:111) ~[spring-data-commons-3.2.0.jar:3.2.0]
	... 59 common frames omitted
Caused by: java.lang.IllegalArgumentException: org.hibernate.query.sqm.UnknownPathException: Could not resolve attribute 'userId' of 'com.psychology.platform.entity.Appointment' [SELECT a FROM Appointment a WHERE a.userId = :userId AND DATE(a.scheduledTime) = DATE(:today)]
	at org.hibernate.internal.ExceptionConverterImpl.convert(ExceptionConverterImpl.java:143) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.internal.ExceptionConverterImpl.convert(ExceptionConverterImpl.java:167) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.internal.ExceptionConverterImpl.convert(ExceptionConverterImpl.java:173) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.internal.AbstractSharedSessionContract.createQuery(AbstractSharedSessionContract.java:802) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.internal.AbstractSharedSessionContract.createQuery(AbstractSharedSessionContract.java:707) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.internal.AbstractSharedSessionContract.createQuery(AbstractSharedSessionContract.java:132) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:580) ~[na:na]
	at org.springframework.orm.jpa.ExtendedEntityManagerCreator$ExtendedEntityManagerInvocationHandler.invoke(ExtendedEntityManagerCreator.java:360) ~[spring-orm-6.1.1.jar:6.1.1]
	at jdk.proxy4/jdk.proxy4.$Proxy152.createQuery(Unknown Source) ~[na:na]
	at org.springframework.data.jpa.repository.query.SimpleJpaQuery.validateQuery(SimpleJpaQuery.java:94) ~[spring-data-jpa-3.2.0.jar:3.2.0]
	... 65 common frames omitted
Caused by: org.hibernate.query.sqm.UnknownPathException: Could not resolve attribute 'userId' of 'com.psychology.platform.entity.Appointment' [SELECT a FROM Appointment a WHERE a.userId = :userId AND DATE(a.scheduledTime) = DATE(:today)]
	at org.hibernate.query.hql.internal.StandardHqlTranslator.translate(StandardHqlTranslator.java:87) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.internal.QueryInterpretationCacheStandardImpl.createHqlInterpretation(QueryInterpretationCacheStandardImpl.java:165) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.internal.QueryInterpretationCacheStandardImpl.resolveHqlInterpretation(QueryInterpretationCacheStandardImpl.java:147) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.internal.AbstractSharedSessionContract.interpretHql(AbstractSharedSessionContract.java:744) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.internal.AbstractSharedSessionContract.createQuery(AbstractSharedSessionContract.java:794) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	... 72 common frames omitted
Caused by: org.hibernate.query.sqm.PathElementException: Could not resolve attribute 'userId' of 'com.psychology.platform.entity.Appointment'
	at org.hibernate.query.sqm.SqmPathSource.getSubPathSource(SqmPathSource.java:95) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.sqm.tree.domain.AbstractSqmPath.get(AbstractSqmPath.java:195) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.sqm.tree.domain.AbstractSqmFrom.resolvePathPart(AbstractSqmFrom.java:196) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.hql.internal.DomainPathPart.resolvePathPart(DomainPathPart.java:42) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.hql.internal.BasicDotIdentifierConsumer.consumeIdentifier(BasicDotIdentifierConsumer.java:91) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitSimplePath(SemanticQueryBuilder.java:5010) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitIndexedPathAccessFragment(SemanticQueryBuilder.java:4959) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitGeneralPathFragment(SemanticQueryBuilder.java:4934) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitGeneralPathExpression(SemanticQueryBuilder.java:1775) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.grammars.hql.HqlParser$GeneralPathExpressionContext.accept(HqlParser.java:7571) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.antlr.v4.runtime.tree.AbstractParseTreeVisitor.visitChildren(AbstractParseTreeVisitor.java:46) ~[antlr4-runtime-4.10.1.jar:4.10.1]
	at org.hibernate.grammars.hql.HqlParserBaseVisitor.visitBarePrimaryExpression(HqlParserBaseVisitor.java:755) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.grammars.hql.HqlParser$BarePrimaryExpressionContext.accept(HqlParser.java:7045) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.createComparisonPredicate(SemanticQueryBuilder.java:2428) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitComparisonPredicate(SemanticQueryBuilder.java:2391) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitComparisonPredicate(SemanticQueryBuilder.java:268) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.grammars.hql.HqlParser$ComparisonPredicateContext.accept(HqlParser.java:6071) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitAndPredicate(SemanticQueryBuilder.java:2260) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitAndPredicate(SemanticQueryBuilder.java:268) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.grammars.hql.HqlParser$AndPredicateContext.accept(HqlParser.java:5951) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitWhereClause(SemanticQueryBuilder.java:2243) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitWhereClause(SemanticQueryBuilder.java:268) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.grammars.hql.HqlParser$WhereClauseContext.accept(HqlParser.java:5822) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitQuery(SemanticQueryBuilder.java:1158) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitQuerySpecExpression(SemanticQueryBuilder.java:940) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitQuerySpecExpression(SemanticQueryBuilder.java:268) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.grammars.hql.HqlParser$QuerySpecExpressionContext.accept(HqlParser.java:1844) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitSimpleQueryGroup(SemanticQueryBuilder.java:925) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitSimpleQueryGroup(SemanticQueryBuilder.java:268) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.grammars.hql.HqlParser$SimpleQueryGroupContext.accept(HqlParser.java:1718) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitSelectStatement(SemanticQueryBuilder.java:442) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitStatement(SemanticQueryBuilder.java:401) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.buildSemanticModel(SemanticQueryBuilder.java:310) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.hql.internal.StandardHqlTranslator.translate(StandardHqlTranslator.java:71) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	... 76 common frames omitted

2025-08-31 21:45:14.261  INFO 18404 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : Starting PsychologyPlatformApplication using Java 21.0.8 with PID 18404 (C:\Users\<USER>\Desktop\a\backend\target\classes started by 21653 in C:\Users\<USER>\Desktop\a\backend)
2025-08-31 21:45:14.263  INFO 18404 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : No active profile set, falling back to 1 default profile: "default"
2025-08-31 21:45:14.316  INFO 18404 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-31 21:45:14.317  INFO 18404 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-31 21:45:14.935  INFO 18404 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-31 21:45:14.976  INFO 18404 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 33 ms. Found 4 JPA repository interfaces.
2025-08-31 21:45:15.766  INFO 18404 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-08-31 21:45:15.777  INFO 18404 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-31 21:45:15.778  INFO 18404 --- [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-08-31 21:45:15.843  INFO 18404 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring embedded WebApplicationContext
2025-08-31 21:45:15.843  INFO 18404 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1525 ms
2025-08-31 21:45:15.964  INFO 18404 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-31 21:45:16.034  INFO 18404 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.3.1.Final
2025-08-31 21:45:16.066  INFO 18404 --- [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-31 21:45:16.279  INFO 18404 --- [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-31 21:45:16.303  INFO 18404 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Starting...
2025-08-31 21:45:16.445  INFO 18404 --- [restartedMain] com.zaxxer.hikari.pool.HikariPool        : PsychologyPlatformHikariCP - Added connection com.mysql.cj.jdbc.ConnectionImpl@34edbc35
2025-08-31 21:45:16.446  INFO 18404 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Start completed.
2025-08-31 21:45:16.479  WARN 18404 --- [restartedMain] org.hibernate.orm.deprecation            : HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-31 21:45:17.620  INFO 18404 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-31 21:45:17.645  INFO 18404 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-31 21:45:18.118  INFO 18404 --- [restartedMain] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-08-31 21:45:18.823  WARN 18404 --- [restartedMain] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'appointmentController': Unsatisfied dependency expressed through field 'appointmentService': Error creating bean with name 'appointmentServiceImpl': Unsatisfied dependency expressed through field 'appointmentRepository': Error creating bean with name 'appointmentRepository' defined in com.psychology.platform.repository.AppointmentRepository defined in @EnableJpaRepositories declared on DatabaseConfig: Could not create query for public abstract java.util.List com.psychology.platform.repository.AppointmentRepository.findUserTodayAppointments(java.lang.Long,java.time.LocalDateTime); Reason: Validation failed for query for method public abstract java.util.List com.psychology.platform.repository.AppointmentRepository.findUserTodayAppointments(java.lang.Long,java.time.LocalDateTime)
2025-08-31 21:45:18.823  INFO 18404 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-31 21:45:18.825  INFO 18404 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Shutdown initiated...
2025-08-31 21:45:18.831  INFO 18404 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Shutdown completed.
2025-08-31 21:45:18.834  INFO 18404 --- [restartedMain] o.apache.catalina.core.StandardService   : Stopping service [Tomcat]
2025-08-31 21:45:18.846  INFO 18404 --- [restartedMain] .s.b.a.l.ConditionEvaluationReportLogger : 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-31 21:45:18.880 ERROR 18404 --- [restartedMain] o.s.boot.SpringApplication               : Application run failed

org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'appointmentController': Unsatisfied dependency expressed through field 'appointmentService': Error creating bean with name 'appointmentServiceImpl': Unsatisfied dependency expressed through field 'appointmentRepository': Error creating bean with name 'appointmentRepository' defined in com.psychology.platform.repository.AppointmentRepository defined in @EnableJpaRepositories declared on DatabaseConfig: Could not create query for public abstract java.util.List com.psychology.platform.repository.AppointmentRepository.findUserTodayAppointments(java.lang.Long,java.time.LocalDateTime); Reason: Validation failed for query for method public abstract java.util.List com.psychology.platform.repository.AppointmentRepository.findUserTodayAppointments(java.lang.Long,java.time.LocalDateTime)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:772) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:752) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:493) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1420) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:600) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:973) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:946) ~[spring-context-6.1.1.jar:6.1.1]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:616) ~[spring-context-6.1.1.jar:6.1.1]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146) ~[spring-boot-3.2.0.jar:3.2.0]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:753) ~[spring-boot-3.2.0.jar:3.2.0]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:455) ~[spring-boot-3.2.0.jar:3.2.0]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:323) ~[spring-boot-3.2.0.jar:3.2.0]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1342) ~[spring-boot-3.2.0.jar:3.2.0]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1331) ~[spring-boot-3.2.0.jar:3.2.0]
	at com.psychology.platform.PsychologyPlatformApplication.main(PsychologyPlatformApplication.java:21) ~[classes/:na]
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:580) ~[na:na]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) ~[spring-boot-devtools-3.2.0.jar:3.2.0]
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'appointmentServiceImpl': Unsatisfied dependency expressed through field 'appointmentRepository': Error creating bean with name 'appointmentRepository' defined in com.psychology.platform.repository.AppointmentRepository defined in @EnableJpaRepositories declared on DatabaseConfig: Could not create query for public abstract java.util.List com.psychology.platform.repository.AppointmentRepository.findUserTodayAppointments(java.lang.Long,java.time.LocalDateTime); Reason: Validation failed for query for method public abstract java.util.List com.psychology.platform.repository.AppointmentRepository.findUserTodayAppointments(java.lang.Long,java.time.LocalDateTime)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:772) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:752) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:493) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1420) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:600) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1441) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1348) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:769) ~[spring-beans-6.1.1.jar:6.1.1]
	... 23 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'appointmentRepository' defined in com.psychology.platform.repository.AppointmentRepository defined in @EnableJpaRepositories declared on DatabaseConfig: Could not create query for public abstract java.util.List com.psychology.platform.repository.AppointmentRepository.findUserTodayAppointments(java.lang.Long,java.time.LocalDateTime); Reason: Validation failed for query for method public abstract java.util.List com.psychology.platform.repository.AppointmentRepository.findUserTodayAppointments(java.lang.Long,java.time.LocalDateTime)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1775) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:601) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1441) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1348) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:769) ~[spring-beans-6.1.1.jar:6.1.1]
	... 37 common frames omitted
Caused by: org.springframework.data.repository.query.QueryCreationException: Could not create query for public abstract java.util.List com.psychology.platform.repository.AppointmentRepository.findUserTodayAppointments(java.lang.Long,java.time.LocalDateTime); Reason: Validation failed for query for method public abstract java.util.List com.psychology.platform.repository.AppointmentRepository.findUserTodayAppointments(java.lang.Long,java.time.LocalDateTime)
	at org.springframework.data.repository.query.QueryCreationException.create(QueryCreationException.java:101) ~[spring-data-commons-3.2.0.jar:3.2.0]
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:115) ~[spring-data-commons-3.2.0.jar:3.2.0]
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.mapMethodsToQuery(QueryExecutorMethodInterceptor.java:99) ~[spring-data-commons-3.2.0.jar:3.2.0]
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lambda$new$0(QueryExecutorMethodInterceptor.java:88) ~[spring-data-commons-3.2.0.jar:3.2.0]
	at java.base/java.util.Optional.map(Optional.java:260) ~[na:na]
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.<init>(QueryExecutorMethodInterceptor.java:88) ~[spring-data-commons-3.2.0.jar:3.2.0]
	at org.springframework.data.repository.core.support.RepositoryFactorySupport.getRepository(RepositoryFactorySupport.java:357) ~[spring-data-commons-3.2.0.jar:3.2.0]
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.lambda$afterPropertiesSet$5(RepositoryFactoryBeanSupport.java:279) ~[spring-data-commons-3.2.0.jar:3.2.0]
	at org.springframework.data.util.Lazy.getNullable(Lazy.java:135) ~[spring-data-commons-3.2.0.jar:3.2.0]
	at org.springframework.data.util.Lazy.get(Lazy.java:113) ~[spring-data-commons-3.2.0.jar:3.2.0]
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.afterPropertiesSet(RepositoryFactoryBeanSupport.java:285) ~[spring-data-commons-3.2.0.jar:3.2.0]
	at org.springframework.data.jpa.repository.support.JpaRepositoryFactoryBean.afterPropertiesSet(JpaRepositoryFactoryBean.java:132) ~[spring-data-jpa-3.2.0.jar:3.2.0]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1822) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1771) ~[spring-beans-6.1.1.jar:6.1.1]
	... 47 common frames omitted
Caused by: java.lang.IllegalArgumentException: Validation failed for query for method public abstract java.util.List com.psychology.platform.repository.AppointmentRepository.findUserTodayAppointments(java.lang.Long,java.time.LocalDateTime)
	at org.springframework.data.jpa.repository.query.SimpleJpaQuery.validateQuery(SimpleJpaQuery.java:100) ~[spring-data-jpa-3.2.0.jar:3.2.0]
	at org.springframework.data.jpa.repository.query.SimpleJpaQuery.<init>(SimpleJpaQuery.java:70) ~[spring-data-jpa-3.2.0.jar:3.2.0]
	at org.springframework.data.jpa.repository.query.JpaQueryFactory.fromMethodWithQueryString(JpaQueryFactory.java:60) ~[spring-data-jpa-3.2.0.jar:3.2.0]
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$DeclaredQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:170) ~[spring-data-jpa-3.2.0.jar:3.2.0]
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$CreateIfNotFoundQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:252) ~[spring-data-jpa-3.2.0.jar:3.2.0]
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$AbstractQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:95) ~[spring-data-jpa-3.2.0.jar:3.2.0]
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:111) ~[spring-data-commons-3.2.0.jar:3.2.0]
	... 59 common frames omitted
Caused by: java.lang.IllegalArgumentException: org.hibernate.query.sqm.UnknownPathException: Could not resolve attribute 'userId' of 'com.psychology.platform.entity.Appointment' [SELECT a FROM Appointment a WHERE a.userId = :userId AND DATE(a.scheduledTime) = DATE(:today)]
	at org.hibernate.internal.ExceptionConverterImpl.convert(ExceptionConverterImpl.java:143) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.internal.ExceptionConverterImpl.convert(ExceptionConverterImpl.java:167) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.internal.ExceptionConverterImpl.convert(ExceptionConverterImpl.java:173) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.internal.AbstractSharedSessionContract.createQuery(AbstractSharedSessionContract.java:802) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.internal.AbstractSharedSessionContract.createQuery(AbstractSharedSessionContract.java:707) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.internal.AbstractSharedSessionContract.createQuery(AbstractSharedSessionContract.java:132) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:580) ~[na:na]
	at org.springframework.orm.jpa.ExtendedEntityManagerCreator$ExtendedEntityManagerInvocationHandler.invoke(ExtendedEntityManagerCreator.java:360) ~[spring-orm-6.1.1.jar:6.1.1]
	at jdk.proxy4/jdk.proxy4.$Proxy152.createQuery(Unknown Source) ~[na:na]
	at org.springframework.data.jpa.repository.query.SimpleJpaQuery.validateQuery(SimpleJpaQuery.java:94) ~[spring-data-jpa-3.2.0.jar:3.2.0]
	... 65 common frames omitted
Caused by: org.hibernate.query.sqm.UnknownPathException: Could not resolve attribute 'userId' of 'com.psychology.platform.entity.Appointment' [SELECT a FROM Appointment a WHERE a.userId = :userId AND DATE(a.scheduledTime) = DATE(:today)]
	at org.hibernate.query.hql.internal.StandardHqlTranslator.translate(StandardHqlTranslator.java:87) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.internal.QueryInterpretationCacheStandardImpl.createHqlInterpretation(QueryInterpretationCacheStandardImpl.java:165) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.internal.QueryInterpretationCacheStandardImpl.resolveHqlInterpretation(QueryInterpretationCacheStandardImpl.java:147) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.internal.AbstractSharedSessionContract.interpretHql(AbstractSharedSessionContract.java:744) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.internal.AbstractSharedSessionContract.createQuery(AbstractSharedSessionContract.java:794) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	... 72 common frames omitted
Caused by: org.hibernate.query.sqm.PathElementException: Could not resolve attribute 'userId' of 'com.psychology.platform.entity.Appointment'
	at org.hibernate.query.sqm.SqmPathSource.getSubPathSource(SqmPathSource.java:95) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.sqm.tree.domain.AbstractSqmPath.get(AbstractSqmPath.java:195) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.sqm.tree.domain.AbstractSqmFrom.resolvePathPart(AbstractSqmFrom.java:196) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.hql.internal.DomainPathPart.resolvePathPart(DomainPathPart.java:42) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.hql.internal.BasicDotIdentifierConsumer.consumeIdentifier(BasicDotIdentifierConsumer.java:91) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitSimplePath(SemanticQueryBuilder.java:5010) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitIndexedPathAccessFragment(SemanticQueryBuilder.java:4959) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitGeneralPathFragment(SemanticQueryBuilder.java:4934) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitGeneralPathExpression(SemanticQueryBuilder.java:1775) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.grammars.hql.HqlParser$GeneralPathExpressionContext.accept(HqlParser.java:7571) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.antlr.v4.runtime.tree.AbstractParseTreeVisitor.visitChildren(AbstractParseTreeVisitor.java:46) ~[antlr4-runtime-4.10.1.jar:4.10.1]
	at org.hibernate.grammars.hql.HqlParserBaseVisitor.visitBarePrimaryExpression(HqlParserBaseVisitor.java:755) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.grammars.hql.HqlParser$BarePrimaryExpressionContext.accept(HqlParser.java:7045) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.createComparisonPredicate(SemanticQueryBuilder.java:2428) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitComparisonPredicate(SemanticQueryBuilder.java:2391) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitComparisonPredicate(SemanticQueryBuilder.java:268) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.grammars.hql.HqlParser$ComparisonPredicateContext.accept(HqlParser.java:6071) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitAndPredicate(SemanticQueryBuilder.java:2260) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitAndPredicate(SemanticQueryBuilder.java:268) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.grammars.hql.HqlParser$AndPredicateContext.accept(HqlParser.java:5951) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitWhereClause(SemanticQueryBuilder.java:2243) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitWhereClause(SemanticQueryBuilder.java:268) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.grammars.hql.HqlParser$WhereClauseContext.accept(HqlParser.java:5822) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitQuery(SemanticQueryBuilder.java:1158) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitQuerySpecExpression(SemanticQueryBuilder.java:940) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitQuerySpecExpression(SemanticQueryBuilder.java:268) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.grammars.hql.HqlParser$QuerySpecExpressionContext.accept(HqlParser.java:1844) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitSimpleQueryGroup(SemanticQueryBuilder.java:925) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitSimpleQueryGroup(SemanticQueryBuilder.java:268) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.grammars.hql.HqlParser$SimpleQueryGroupContext.accept(HqlParser.java:1718) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitSelectStatement(SemanticQueryBuilder.java:442) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitStatement(SemanticQueryBuilder.java:401) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.buildSemanticModel(SemanticQueryBuilder.java:310) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.hql.internal.StandardHqlTranslator.translate(StandardHqlTranslator.java:71) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	... 76 common frames omitted

2025-08-31 21:46:58.579  INFO 18704 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : Starting PsychologyPlatformApplication using Java 21.0.8 with PID 18704 (C:\Users\<USER>\Desktop\a\backend\target\classes started by 21653 in C:\Users\<USER>\Desktop\a\backend)
2025-08-31 21:46:58.583  INFO 18704 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : No active profile set, falling back to 1 default profile: "default"
2025-08-31 21:46:58.646  INFO 18704 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-31 21:46:58.646  INFO 18704 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-31 21:46:59.395  INFO 18704 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-31 21:46:59.444  INFO 18704 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 42 ms. Found 4 JPA repository interfaces.
2025-08-31 21:47:00.287  INFO 18704 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-08-31 21:47:00.295  INFO 18704 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-31 21:47:00.295  INFO 18704 --- [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-08-31 21:47:00.341  INFO 18704 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring embedded WebApplicationContext
2025-08-31 21:47:00.342  INFO 18704 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1695 ms
2025-08-31 21:47:00.482  INFO 18704 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-31 21:47:00.550  INFO 18704 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.3.1.Final
2025-08-31 21:47:00.595  INFO 18704 --- [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-31 21:47:00.998  INFO 18704 --- [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-31 21:47:01.203  INFO 18704 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Starting...
2025-08-31 21:47:01.821  INFO 18704 --- [restartedMain] com.zaxxer.hikari.pool.HikariPool        : PsychologyPlatformHikariCP - Added connection com.mysql.cj.jdbc.ConnectionImpl@59a63f37
2025-08-31 21:47:01.825  INFO 18704 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Start completed.
2025-08-31 21:47:01.891  WARN 18704 --- [restartedMain] org.hibernate.orm.deprecation            : HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-31 21:47:02.332 ERROR 18704 --- [restartedMain] o.s.b.web.embedded.tomcat.TomcatStarter  : Error starting Tomcat context. Exception: org.springframework.beans.factory.UnsatisfiedDependencyException. Message: Error creating bean with name 'securityConfig': Unsatisfied dependency expressed through field 'userDetailsService': Error creating bean with name 'userDetailsServiceImpl': Unsatisfied dependency expressed through field 'userRepository': Error creating bean with name 'userRepository' defined in com.psychology.platform.repository.UserRepository defined in @EnableJpaRepositories declared on DatabaseConfig: Cannot resolve reference to bean 'jpaSharedEM_entityManagerFactory' while setting bean property 'entityManager'
2025-08-31 21:47:02.396  INFO 18704 --- [restartedMain] o.apache.catalina.core.StandardService   : Stopping service [Tomcat]
2025-08-31 21:47:02.405  WARN 18704 --- [restartedMain] o.a.c.loader.WebappClassLoaderBase       : The web application [api] appears to have started a thread named [PsychologyPlatformHikariCP housekeeper] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base/jdk.internal.misc.Unsafe.park(Native Method)
 java.base/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
 java.base/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1763)
 java.base/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182)
 java.base/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
 java.base/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070)
 java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
 java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
 java.base/java.lang.Thread.run(Thread.java:1583)
2025-08-31 21:47:02.405  WARN 18704 --- [restartedMain] o.a.c.loader.WebappClassLoaderBase       : The web application [api] appears to have started a thread named [PsychologyPlatformHikariCP connection adder] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.base/jdk.internal.misc.Unsafe.park(Native Method)
 java.base/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
 java.base/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1763)
 java.base/java.util.concurrent.LinkedBlockingQueue.poll(LinkedBlockingQueue.java:460)
 java.base/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1069)
 java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
 java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
 java.base/java.lang.Thread.run(Thread.java:1583)
2025-08-31 21:47:02.410  WARN 18704 --- [restartedMain] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Unable to start web server
2025-08-31 21:47:02.413  INFO 18704 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Shutdown initiated...
2025-08-31 21:47:02.429  INFO 18704 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Shutdown completed.
2025-08-31 21:47:02.449  INFO 18704 --- [restartedMain] .s.b.a.l.ConditionEvaluationReportLogger : 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-31 21:47:02.518 ERROR 18704 --- [restartedMain] o.s.boot.SpringApplication               : Application run failed

org.springframework.context.ApplicationContextException: Unable to start web server
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:165) ~[spring-boot-3.2.0.jar:3.2.0]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:610) ~[spring-context-6.1.1.jar:6.1.1]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146) ~[spring-boot-3.2.0.jar:3.2.0]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:753) ~[spring-boot-3.2.0.jar:3.2.0]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:455) ~[spring-boot-3.2.0.jar:3.2.0]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:323) ~[spring-boot-3.2.0.jar:3.2.0]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1342) ~[spring-boot-3.2.0.jar:3.2.0]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1331) ~[spring-boot-3.2.0.jar:3.2.0]
	at com.psychology.platform.PsychologyPlatformApplication.main(PsychologyPlatformApplication.java:21) ~[classes/:na]
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:580) ~[na:na]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) ~[spring-boot-devtools-3.2.0.jar:3.2.0]
Caused by: org.springframework.boot.web.server.WebServerException: Unable to start embedded Tomcat
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize(TomcatWebServer.java:142) ~[spring-boot-3.2.0.jar:3.2.0]
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.<init>(TomcatWebServer.java:104) ~[spring-boot-3.2.0.jar:3.2.0]
	at org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory.getTomcatWebServer(TomcatServletWebServerFactory.java:501) ~[spring-boot-3.2.0.jar:3.2.0]
	at org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory.getWebServer(TomcatServletWebServerFactory.java:218) ~[spring-boot-3.2.0.jar:3.2.0]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.createWebServer(ServletWebServerApplicationContext.java:188) ~[spring-boot-3.2.0.jar:3.2.0]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:162) ~[spring-boot-3.2.0.jar:3.2.0]
	... 11 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'securityConfig': Unsatisfied dependency expressed through field 'userDetailsService': Error creating bean with name 'userDetailsServiceImpl': Unsatisfied dependency expressed through field 'userRepository': Error creating bean with name 'userRepository' defined in com.psychology.platform.repository.UserRepository defined in @EnableJpaRepositories declared on DatabaseConfig: Cannot resolve reference to bean 'jpaSharedEM_entityManagerFactory' while setting bean property 'entityManager'
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:772) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:752) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:493) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1420) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:600) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:413) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1336) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1166) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:204) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:210) ~[spring-boot-3.2.0.jar:3.2.0]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addAsRegistrationBean(ServletContextInitializerBeans.java:173) ~[spring-boot-3.2.0.jar:3.2.0]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addAsRegistrationBean(ServletContextInitializerBeans.java:168) ~[spring-boot-3.2.0.jar:3.2.0]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addAdaptableBeans(ServletContextInitializerBeans.java:153) ~[spring-boot-3.2.0.jar:3.2.0]
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.<init>(ServletContextInitializerBeans.java:86) ~[spring-boot-3.2.0.jar:3.2.0]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.getServletContextInitializerBeans(ServletWebServerApplicationContext.java:266) ~[spring-boot-3.2.0.jar:3.2.0]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.selfInitialize(ServletWebServerApplicationContext.java:240) ~[spring-boot-3.2.0.jar:3.2.0]
	at org.springframework.boot.web.embedded.tomcat.TomcatStarter.onStartup(TomcatStarter.java:52) ~[spring-boot-3.2.0.jar:3.2.0]
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:4850) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:171) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1332) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1322) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317) ~[na:na]
	at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:145) ~[na:na]
	at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:866) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:845) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:171) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1332) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1322) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317) ~[na:na]
	at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:145) ~[na:na]
	at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:866) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:240) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:171) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.core.StandardService.startInternal(StandardService.java:433) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:171) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:917) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:171) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.apache.catalina.startup.Tomcat.start(Tomcat.java:488) ~[tomcat-embed-core-10.1.16.jar:10.1.16]
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize(TomcatWebServer.java:123) ~[spring-boot-3.2.0.jar:3.2.0]
	... 16 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'userDetailsServiceImpl': Unsatisfied dependency expressed through field 'userRepository': Error creating bean with name 'userRepository' defined in com.psychology.platform.repository.UserRepository defined in @EnableJpaRepositories declared on DatabaseConfig: Cannot resolve reference to bean 'jpaSharedEM_entityManagerFactory' while setting bean property 'entityManager'
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:772) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:752) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:493) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1420) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:600) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1441) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1348) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:769) ~[spring-beans-6.1.1.jar:6.1.1]
	... 67 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'userRepository' defined in com.psychology.platform.repository.UserRepository defined in @EnableJpaRepositories declared on DatabaseConfig: Cannot resolve reference to bean 'jpaSharedEM_entityManagerFactory' while setting bean property 'entityManager'
	at org.springframework.beans.factory.support.BeanDefinitionValueResolver.resolveReference(BeanDefinitionValueResolver.java:377) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.BeanDefinitionValueResolver.resolveValueIfNecessary(BeanDefinitionValueResolver.java:135) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyPropertyValues(AbstractAutowireCapableBeanFactory.java:1686) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1435) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:600) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1441) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1348) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:769) ~[spring-beans-6.1.1.jar:6.1.1]
	... 81 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'jpaSharedEM_entityManagerFactory': Cannot resolve reference to bean 'entityManagerFactory' while setting constructor argument
	at org.springframework.beans.factory.support.BeanDefinitionValueResolver.resolveReference(BeanDefinitionValueResolver.java:377) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.BeanDefinitionValueResolver.resolveValueIfNecessary(BeanDefinitionValueResolver.java:135) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.ConstructorResolver.resolveConstructorArguments(ConstructorResolver.java:689) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:513) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1336) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1166) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.BeanDefinitionValueResolver.resolveReference(BeanDefinitionValueResolver.java:365) ~[spring-beans-6.1.1.jar:6.1.1]
	... 94 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'entityManagerFactory' defined in class path resource [org/springframework/boot/autoconfigure/orm/jpa/HibernateJpaConfiguration.class]: com/psychology/platform/entity/User$Gender
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1775) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:601) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.BeanDefinitionValueResolver.resolveReference(BeanDefinitionValueResolver.java:365) ~[spring-beans-6.1.1.jar:6.1.1]
	... 106 common frames omitted
Caused by: java.lang.NoClassDefFoundError: com/psychology/platform/entity/User$Gender
	at java.base/java.lang.Class.getDeclaredMethods0(Native Method) ~[na:na]
	at java.base/java.lang.Class.privateGetDeclaredMethods(Class.java:3578) ~[na:na]
	at java.base/java.lang.Class.getDeclaredMethods(Class.java:2676) ~[na:na]
	at org.hibernate.annotations.common.reflection.java.JavaXClass.getDeclaredMethodProperties(JavaXClass.java:137) ~[hibernate-commons-annotations-6.0.6.Final.jar:6.0.6.Final]
	at org.hibernate.annotations.common.reflection.java.JavaXClass.getDeclaredProperties(JavaXClass.java:175) ~[hibernate-commons-annotations-6.0.6.Final.jar:6.0.6.Final]
	at org.hibernate.annotations.common.reflection.java.JavaXClass.getDeclaredProperties(JavaXClass.java:167) ~[hibernate-commons-annotations-6.0.6.Final.jar:6.0.6.Final]
	at org.hibernate.boot.model.internal.InheritanceState.determineDefaultAccessType(InheritanceState.java:259) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.boot.model.internal.InheritanceState.getElementsToProcess(InheritanceState.java:215) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.boot.model.internal.InheritanceState.postProcess(InheritanceState.java:160) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.boot.model.internal.EntityBinder.handleIdentifier(EntityBinder.java:296) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.boot.model.internal.EntityBinder.bindEntityClass(EntityBinder.java:231) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.boot.model.internal.AnnotationBinder.bindClass(AnnotationBinder.java:422) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.boot.model.source.internal.annotations.AnnotationMetadataSourceProcessorImpl.processEntityHierarchies(AnnotationMetadataSourceProcessorImpl.java:255) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.boot.model.process.spi.MetadataBuildingProcess$1.processEntityHierarchies(MetadataBuildingProcess.java:278) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.boot.model.process.spi.MetadataBuildingProcess.complete(MetadataBuildingProcess.java:321) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.metadata(EntityManagerFactoryBuilderImpl.java:1432) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.build(EntityManagerFactoryBuilderImpl.java:1503) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.springframework.orm.jpa.vendor.SpringHibernateJpaPersistenceProvider.createContainerEntityManagerFactory(SpringHibernateJpaPersistenceProvider.java:75) ~[spring-orm-6.1.1.jar:6.1.1]
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.createNativeEntityManagerFactory(LocalContainerEntityManagerFactoryBean.java:376) ~[spring-orm-6.1.1.jar:6.1.1]
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.buildNativeEntityManagerFactory(AbstractEntityManagerFactoryBean.java:409) ~[spring-orm-6.1.1.jar:6.1.1]
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.afterPropertiesSet(AbstractEntityManagerFactoryBean.java:396) ~[spring-orm-6.1.1.jar:6.1.1]
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.afterPropertiesSet(LocalContainerEntityManagerFactoryBean.java:352) ~[spring-orm-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1822) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1771) ~[spring-beans-6.1.1.jar:6.1.1]
	... 113 common frames omitted
Caused by: java.lang.ClassNotFoundException: com.psychology.platform.entity.User$Gender
	at java.base/jdk.internal.loader.BuiltinClassLoader.loadClass(BuiltinClassLoader.java:641) ~[na:na]
	at java.base/jdk.internal.loader.ClassLoaders$AppClassLoader.loadClass(ClassLoaders.java:188) ~[na:na]
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526) ~[na:na]
	at java.base/java.lang.Class.forName0(Native Method) ~[na:na]
	at java.base/java.lang.Class.forName(Class.java:534) ~[na:na]
	at java.base/java.lang.Class.forName(Class.java:513) ~[na:na]
	at org.springframework.boot.devtools.restart.classloader.RestartClassLoader.loadClass(RestartClassLoader.java:121) ~[spring-boot-devtools-3.2.0.jar:3.2.0]
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:526) ~[na:na]
	... 137 common frames omitted

2025-08-31 21:48:28.758  INFO 9844 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : Starting PsychologyPlatformApplication using Java 21.0.8 with PID 9844 (C:\Users\<USER>\Desktop\a\backend\target\classes started by 21653 in C:\Users\<USER>\Desktop\a\backend)
2025-08-31 21:48:28.760  INFO 9844 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : No active profile set, falling back to 1 default profile: "default"
2025-08-31 21:48:28.812  INFO 9844 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-31 21:48:28.812  INFO 9844 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-31 21:48:29.654  INFO 9844 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-31 21:48:29.710  INFO 9844 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 47 ms. Found 4 JPA repository interfaces.
2025-08-31 21:48:30.548  INFO 9844 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-08-31 21:48:30.559  INFO 9844 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-31 21:48:30.559  INFO 9844 --- [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-08-31 21:48:30.604  INFO 9844 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring embedded WebApplicationContext
2025-08-31 21:48:30.604  INFO 9844 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1791 ms
2025-08-31 21:48:30.738  INFO 9844 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-31 21:48:30.804  INFO 9844 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.3.1.Final
2025-08-31 21:48:30.840  INFO 9844 --- [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-31 21:48:31.052  INFO 9844 --- [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-31 21:48:31.077  INFO 9844 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Starting...
2025-08-31 21:48:31.229  INFO 9844 --- [restartedMain] com.zaxxer.hikari.pool.HikariPool        : PsychologyPlatformHikariCP - Added connection com.mysql.cj.jdbc.ConnectionImpl@4f8318e1
2025-08-31 21:48:31.232  INFO 9844 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Start completed.
2025-08-31 21:48:31.267  WARN 9844 --- [restartedMain] org.hibernate.orm.deprecation            : HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-31 21:48:32.224  INFO 9844 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-31 21:48:32.228  INFO 9844 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-31 21:48:32.613  INFO 9844 --- [restartedMain] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-08-31 21:48:33.251  WARN 9844 --- [restartedMain] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'appointmentController': Unsatisfied dependency expressed through field 'appointmentService': Error creating bean with name 'appointmentServiceImpl': Unsatisfied dependency expressed through field 'appointmentRepository': Error creating bean with name 'appointmentRepository' defined in com.psychology.platform.repository.AppointmentRepository defined in @EnableJpaRepositories declared on DatabaseConfig: Could not create query for public abstract java.util.List com.psychology.platform.repository.AppointmentRepository.findByCounselorIdAndScheduledTimeBetween(java.lang.Long,java.time.LocalDateTime,java.time.LocalDateTime); Reason: Validation failed for query for method public abstract java.util.List com.psychology.platform.repository.AppointmentRepository.findByCounselorIdAndScheduledTimeBetween(java.lang.Long,java.time.LocalDateTime,java.time.LocalDateTime)
2025-08-31 21:48:33.251  INFO 9844 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-31 21:48:33.253  INFO 9844 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Shutdown initiated...
2025-08-31 21:48:33.258  INFO 9844 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Shutdown completed.
2025-08-31 21:48:33.260  INFO 9844 --- [restartedMain] o.apache.catalina.core.StandardService   : Stopping service [Tomcat]
2025-08-31 21:48:33.272  INFO 9844 --- [restartedMain] .s.b.a.l.ConditionEvaluationReportLogger : 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-31 21:48:33.295 ERROR 9844 --- [restartedMain] o.s.boot.SpringApplication               : Application run failed

org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'appointmentController': Unsatisfied dependency expressed through field 'appointmentService': Error creating bean with name 'appointmentServiceImpl': Unsatisfied dependency expressed through field 'appointmentRepository': Error creating bean with name 'appointmentRepository' defined in com.psychology.platform.repository.AppointmentRepository defined in @EnableJpaRepositories declared on DatabaseConfig: Could not create query for public abstract java.util.List com.psychology.platform.repository.AppointmentRepository.findByCounselorIdAndScheduledTimeBetween(java.lang.Long,java.time.LocalDateTime,java.time.LocalDateTime); Reason: Validation failed for query for method public abstract java.util.List com.psychology.platform.repository.AppointmentRepository.findByCounselorIdAndScheduledTimeBetween(java.lang.Long,java.time.LocalDateTime,java.time.LocalDateTime)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:772) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:752) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:493) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1420) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:600) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:973) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:946) ~[spring-context-6.1.1.jar:6.1.1]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:616) ~[spring-context-6.1.1.jar:6.1.1]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146) ~[spring-boot-3.2.0.jar:3.2.0]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:753) ~[spring-boot-3.2.0.jar:3.2.0]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:455) ~[spring-boot-3.2.0.jar:3.2.0]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:323) ~[spring-boot-3.2.0.jar:3.2.0]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1342) ~[spring-boot-3.2.0.jar:3.2.0]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1331) ~[spring-boot-3.2.0.jar:3.2.0]
	at com.psychology.platform.PsychologyPlatformApplication.main(PsychologyPlatformApplication.java:17) ~[classes/:na]
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:580) ~[na:na]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) ~[spring-boot-devtools-3.2.0.jar:3.2.0]
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'appointmentServiceImpl': Unsatisfied dependency expressed through field 'appointmentRepository': Error creating bean with name 'appointmentRepository' defined in com.psychology.platform.repository.AppointmentRepository defined in @EnableJpaRepositories declared on DatabaseConfig: Could not create query for public abstract java.util.List com.psychology.platform.repository.AppointmentRepository.findByCounselorIdAndScheduledTimeBetween(java.lang.Long,java.time.LocalDateTime,java.time.LocalDateTime); Reason: Validation failed for query for method public abstract java.util.List com.psychology.platform.repository.AppointmentRepository.findByCounselorIdAndScheduledTimeBetween(java.lang.Long,java.time.LocalDateTime,java.time.LocalDateTime)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:772) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:752) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:493) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1420) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:600) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1441) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1348) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:769) ~[spring-beans-6.1.1.jar:6.1.1]
	... 23 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'appointmentRepository' defined in com.psychology.platform.repository.AppointmentRepository defined in @EnableJpaRepositories declared on DatabaseConfig: Could not create query for public abstract java.util.List com.psychology.platform.repository.AppointmentRepository.findByCounselorIdAndScheduledTimeBetween(java.lang.Long,java.time.LocalDateTime,java.time.LocalDateTime); Reason: Validation failed for query for method public abstract java.util.List com.psychology.platform.repository.AppointmentRepository.findByCounselorIdAndScheduledTimeBetween(java.lang.Long,java.time.LocalDateTime,java.time.LocalDateTime)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1775) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:601) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1441) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1348) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:769) ~[spring-beans-6.1.1.jar:6.1.1]
	... 37 common frames omitted
Caused by: org.springframework.data.repository.query.QueryCreationException: Could not create query for public abstract java.util.List com.psychology.platform.repository.AppointmentRepository.findByCounselorIdAndScheduledTimeBetween(java.lang.Long,java.time.LocalDateTime,java.time.LocalDateTime); Reason: Validation failed for query for method public abstract java.util.List com.psychology.platform.repository.AppointmentRepository.findByCounselorIdAndScheduledTimeBetween(java.lang.Long,java.time.LocalDateTime,java.time.LocalDateTime)
	at org.springframework.data.repository.query.QueryCreationException.create(QueryCreationException.java:101) ~[spring-data-commons-3.2.0.jar:3.2.0]
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:115) ~[spring-data-commons-3.2.0.jar:3.2.0]
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.mapMethodsToQuery(QueryExecutorMethodInterceptor.java:99) ~[spring-data-commons-3.2.0.jar:3.2.0]
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lambda$new$0(QueryExecutorMethodInterceptor.java:88) ~[spring-data-commons-3.2.0.jar:3.2.0]
	at java.base/java.util.Optional.map(Optional.java:260) ~[na:na]
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.<init>(QueryExecutorMethodInterceptor.java:88) ~[spring-data-commons-3.2.0.jar:3.2.0]
	at org.springframework.data.repository.core.support.RepositoryFactorySupport.getRepository(RepositoryFactorySupport.java:357) ~[spring-data-commons-3.2.0.jar:3.2.0]
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.lambda$afterPropertiesSet$5(RepositoryFactoryBeanSupport.java:279) ~[spring-data-commons-3.2.0.jar:3.2.0]
	at org.springframework.data.util.Lazy.getNullable(Lazy.java:135) ~[spring-data-commons-3.2.0.jar:3.2.0]
	at org.springframework.data.util.Lazy.get(Lazy.java:113) ~[spring-data-commons-3.2.0.jar:3.2.0]
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.afterPropertiesSet(RepositoryFactoryBeanSupport.java:285) ~[spring-data-commons-3.2.0.jar:3.2.0]
	at org.springframework.data.jpa.repository.support.JpaRepositoryFactoryBean.afterPropertiesSet(JpaRepositoryFactoryBean.java:132) ~[spring-data-jpa-3.2.0.jar:3.2.0]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1822) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1771) ~[spring-beans-6.1.1.jar:6.1.1]
	... 47 common frames omitted
Caused by: java.lang.IllegalArgumentException: Validation failed for query for method public abstract java.util.List com.psychology.platform.repository.AppointmentRepository.findByCounselorIdAndScheduledTimeBetween(java.lang.Long,java.time.LocalDateTime,java.time.LocalDateTime)
	at org.springframework.data.jpa.repository.query.SimpleJpaQuery.validateQuery(SimpleJpaQuery.java:100) ~[spring-data-jpa-3.2.0.jar:3.2.0]
	at org.springframework.data.jpa.repository.query.SimpleJpaQuery.<init>(SimpleJpaQuery.java:70) ~[spring-data-jpa-3.2.0.jar:3.2.0]
	at org.springframework.data.jpa.repository.query.JpaQueryFactory.fromMethodWithQueryString(JpaQueryFactory.java:60) ~[spring-data-jpa-3.2.0.jar:3.2.0]
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$DeclaredQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:170) ~[spring-data-jpa-3.2.0.jar:3.2.0]
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$CreateIfNotFoundQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:252) ~[spring-data-jpa-3.2.0.jar:3.2.0]
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$AbstractQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:95) ~[spring-data-jpa-3.2.0.jar:3.2.0]
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:111) ~[spring-data-commons-3.2.0.jar:3.2.0]
	... 59 common frames omitted
Caused by: java.lang.IllegalArgumentException: org.hibernate.query.sqm.UnknownPathException: Could not resolve attribute 'counselorId' of 'com.psychology.platform.entity.Appointment' [SELECT a FROM Appointment a WHERE a.counselorId = :counselorId AND a.scheduledTime BETWEEN :startTime AND :endTime]
	at org.hibernate.internal.ExceptionConverterImpl.convert(ExceptionConverterImpl.java:143) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.internal.ExceptionConverterImpl.convert(ExceptionConverterImpl.java:167) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.internal.ExceptionConverterImpl.convert(ExceptionConverterImpl.java:173) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.internal.AbstractSharedSessionContract.createQuery(AbstractSharedSessionContract.java:802) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.internal.AbstractSharedSessionContract.createQuery(AbstractSharedSessionContract.java:707) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.internal.AbstractSharedSessionContract.createQuery(AbstractSharedSessionContract.java:132) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:580) ~[na:na]
	at org.springframework.orm.jpa.ExtendedEntityManagerCreator$ExtendedEntityManagerInvocationHandler.invoke(ExtendedEntityManagerCreator.java:360) ~[spring-orm-6.1.1.jar:6.1.1]
	at jdk.proxy4/jdk.proxy4.$Proxy145.createQuery(Unknown Source) ~[na:na]
	at org.springframework.data.jpa.repository.query.SimpleJpaQuery.validateQuery(SimpleJpaQuery.java:94) ~[spring-data-jpa-3.2.0.jar:3.2.0]
	... 65 common frames omitted
Caused by: org.hibernate.query.sqm.UnknownPathException: Could not resolve attribute 'counselorId' of 'com.psychology.platform.entity.Appointment' [SELECT a FROM Appointment a WHERE a.counselorId = :counselorId AND a.scheduledTime BETWEEN :startTime AND :endTime]
	at org.hibernate.query.hql.internal.StandardHqlTranslator.translate(StandardHqlTranslator.java:87) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.internal.QueryInterpretationCacheStandardImpl.createHqlInterpretation(QueryInterpretationCacheStandardImpl.java:165) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.internal.QueryInterpretationCacheStandardImpl.resolveHqlInterpretation(QueryInterpretationCacheStandardImpl.java:147) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.internal.AbstractSharedSessionContract.interpretHql(AbstractSharedSessionContract.java:744) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.internal.AbstractSharedSessionContract.createQuery(AbstractSharedSessionContract.java:794) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	... 72 common frames omitted
Caused by: org.hibernate.query.sqm.PathElementException: Could not resolve attribute 'counselorId' of 'com.psychology.platform.entity.Appointment'
	at org.hibernate.query.sqm.SqmPathSource.getSubPathSource(SqmPathSource.java:95) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.sqm.tree.domain.AbstractSqmPath.get(AbstractSqmPath.java:195) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.sqm.tree.domain.AbstractSqmFrom.resolvePathPart(AbstractSqmFrom.java:196) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.hql.internal.DomainPathPart.resolvePathPart(DomainPathPart.java:42) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.hql.internal.BasicDotIdentifierConsumer.consumeIdentifier(BasicDotIdentifierConsumer.java:91) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitSimplePath(SemanticQueryBuilder.java:5010) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitIndexedPathAccessFragment(SemanticQueryBuilder.java:4959) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitGeneralPathFragment(SemanticQueryBuilder.java:4934) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitGeneralPathExpression(SemanticQueryBuilder.java:1775) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.grammars.hql.HqlParser$GeneralPathExpressionContext.accept(HqlParser.java:7571) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.antlr.v4.runtime.tree.AbstractParseTreeVisitor.visitChildren(AbstractParseTreeVisitor.java:46) ~[antlr4-runtime-4.10.1.jar:4.10.1]
	at org.hibernate.grammars.hql.HqlParserBaseVisitor.visitBarePrimaryExpression(HqlParserBaseVisitor.java:755) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.grammars.hql.HqlParser$BarePrimaryExpressionContext.accept(HqlParser.java:7045) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.createComparisonPredicate(SemanticQueryBuilder.java:2428) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitComparisonPredicate(SemanticQueryBuilder.java:2391) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitComparisonPredicate(SemanticQueryBuilder.java:268) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.grammars.hql.HqlParser$ComparisonPredicateContext.accept(HqlParser.java:6071) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitAndPredicate(SemanticQueryBuilder.java:2260) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitAndPredicate(SemanticQueryBuilder.java:268) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.grammars.hql.HqlParser$AndPredicateContext.accept(HqlParser.java:5951) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitWhereClause(SemanticQueryBuilder.java:2243) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitWhereClause(SemanticQueryBuilder.java:268) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.grammars.hql.HqlParser$WhereClauseContext.accept(HqlParser.java:5822) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitQuery(SemanticQueryBuilder.java:1158) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitQuerySpecExpression(SemanticQueryBuilder.java:940) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitQuerySpecExpression(SemanticQueryBuilder.java:268) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.grammars.hql.HqlParser$QuerySpecExpressionContext.accept(HqlParser.java:1844) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitSimpleQueryGroup(SemanticQueryBuilder.java:925) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitSimpleQueryGroup(SemanticQueryBuilder.java:268) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.grammars.hql.HqlParser$SimpleQueryGroupContext.accept(HqlParser.java:1718) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitSelectStatement(SemanticQueryBuilder.java:442) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.visitStatement(SemanticQueryBuilder.java:401) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.hql.internal.SemanticQueryBuilder.buildSemanticModel(SemanticQueryBuilder.java:310) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.hql.internal.StandardHqlTranslator.translate(StandardHqlTranslator.java:71) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	... 76 common frames omitted

2025-08-31 21:49:45.519  INFO 16720 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : Starting PsychologyPlatformApplication using Java 21.0.8 with PID 16720 (C:\Users\<USER>\Desktop\a\backend\target\classes started by 21653 in C:\Users\<USER>\Desktop\a\backend)
2025-08-31 21:49:45.521  INFO 16720 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : No active profile set, falling back to 1 default profile: "default"
2025-08-31 21:49:45.598  INFO 16720 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-31 21:49:45.598  INFO 16720 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-31 21:49:46.584  INFO 16720 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-31 21:49:46.653  INFO 16720 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 60 ms. Found 4 JPA repository interfaces.
2025-08-31 21:49:48.171  INFO 16720 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-08-31 21:49:48.184  INFO 16720 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-31 21:49:48.185  INFO 16720 --- [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-08-31 21:49:48.266  INFO 16720 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring embedded WebApplicationContext
2025-08-31 21:49:48.267  INFO 16720 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2668 ms
2025-08-31 21:49:48.478  INFO 16720 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-31 21:49:48.549  INFO 16720 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.3.1.Final
2025-08-31 21:49:48.589  INFO 16720 --- [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-31 21:49:48.910  INFO 16720 --- [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-31 21:49:48.961  INFO 16720 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Starting...
2025-08-31 21:49:49.304  INFO 16720 --- [restartedMain] com.zaxxer.hikari.pool.HikariPool        : PsychologyPlatformHikariCP - Added connection com.mysql.cj.jdbc.ConnectionImpl@651231ea
2025-08-31 21:49:49.308  INFO 16720 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Start completed.
2025-08-31 21:49:49.375  WARN 16720 --- [restartedMain] org.hibernate.orm.deprecation            : HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-31 21:49:50.495  INFO 16720 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-31 21:49:50.498  INFO 16720 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-31 21:49:50.802  INFO 16720 --- [restartedMain] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-08-31 21:49:51.449  WARN 16720 --- [restartedMain] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'appointmentController': Unsatisfied dependency expressed through field 'appointmentService': Error creating bean with name 'appointmentServiceImpl': Unsatisfied dependency expressed through field 'appointmentRepository': Error creating bean with name 'appointmentRepository' defined in com.psychology.platform.repository.AppointmentRepository defined in @EnableJpaRepositories declared on DatabaseConfig: Could not create query for public abstract boolean com.psychology.platform.repository.AppointmentRepository.existsConflictingAppointment(java.lang.Long,java.time.LocalDateTime,java.time.LocalDateTime); Reason: Validation failed for query for method public abstract boolean com.psychology.platform.repository.AppointmentRepository.existsConflictingAppointment(java.lang.Long,java.time.LocalDateTime,java.time.LocalDateTime)
2025-08-31 21:49:51.450  INFO 16720 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-31 21:49:51.452  INFO 16720 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Shutdown initiated...
2025-08-31 21:49:51.457  INFO 16720 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Shutdown completed.
2025-08-31 21:49:51.458  INFO 16720 --- [restartedMain] o.apache.catalina.core.StandardService   : Stopping service [Tomcat]
2025-08-31 21:49:51.469  INFO 16720 --- [restartedMain] .s.b.a.l.ConditionEvaluationReportLogger : 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-31 21:49:51.485 ERROR 16720 --- [restartedMain] o.s.boot.SpringApplication               : Application run failed

org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'appointmentController': Unsatisfied dependency expressed through field 'appointmentService': Error creating bean with name 'appointmentServiceImpl': Unsatisfied dependency expressed through field 'appointmentRepository': Error creating bean with name 'appointmentRepository' defined in com.psychology.platform.repository.AppointmentRepository defined in @EnableJpaRepositories declared on DatabaseConfig: Could not create query for public abstract boolean com.psychology.platform.repository.AppointmentRepository.existsConflictingAppointment(java.lang.Long,java.time.LocalDateTime,java.time.LocalDateTime); Reason: Validation failed for query for method public abstract boolean com.psychology.platform.repository.AppointmentRepository.existsConflictingAppointment(java.lang.Long,java.time.LocalDateTime,java.time.LocalDateTime)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:772) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:752) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:493) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1420) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:600) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:973) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:946) ~[spring-context-6.1.1.jar:6.1.1]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:616) ~[spring-context-6.1.1.jar:6.1.1]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146) ~[spring-boot-3.2.0.jar:3.2.0]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:753) ~[spring-boot-3.2.0.jar:3.2.0]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:455) ~[spring-boot-3.2.0.jar:3.2.0]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:323) ~[spring-boot-3.2.0.jar:3.2.0]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1342) ~[spring-boot-3.2.0.jar:3.2.0]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1331) ~[spring-boot-3.2.0.jar:3.2.0]
	at com.psychology.platform.PsychologyPlatformApplication.main(PsychologyPlatformApplication.java:17) ~[classes/:na]
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:580) ~[na:na]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) ~[spring-boot-devtools-3.2.0.jar:3.2.0]
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'appointmentServiceImpl': Unsatisfied dependency expressed through field 'appointmentRepository': Error creating bean with name 'appointmentRepository' defined in com.psychology.platform.repository.AppointmentRepository defined in @EnableJpaRepositories declared on DatabaseConfig: Could not create query for public abstract boolean com.psychology.platform.repository.AppointmentRepository.existsConflictingAppointment(java.lang.Long,java.time.LocalDateTime,java.time.LocalDateTime); Reason: Validation failed for query for method public abstract boolean com.psychology.platform.repository.AppointmentRepository.existsConflictingAppointment(java.lang.Long,java.time.LocalDateTime,java.time.LocalDateTime)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:772) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:752) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:493) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1420) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:600) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1441) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1348) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:769) ~[spring-beans-6.1.1.jar:6.1.1]
	... 23 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'appointmentRepository' defined in com.psychology.platform.repository.AppointmentRepository defined in @EnableJpaRepositories declared on DatabaseConfig: Could not create query for public abstract boolean com.psychology.platform.repository.AppointmentRepository.existsConflictingAppointment(java.lang.Long,java.time.LocalDateTime,java.time.LocalDateTime); Reason: Validation failed for query for method public abstract boolean com.psychology.platform.repository.AppointmentRepository.existsConflictingAppointment(java.lang.Long,java.time.LocalDateTime,java.time.LocalDateTime)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1775) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:601) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1441) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1348) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:769) ~[spring-beans-6.1.1.jar:6.1.1]
	... 37 common frames omitted
Caused by: org.springframework.data.repository.query.QueryCreationException: Could not create query for public abstract boolean com.psychology.platform.repository.AppointmentRepository.existsConflictingAppointment(java.lang.Long,java.time.LocalDateTime,java.time.LocalDateTime); Reason: Validation failed for query for method public abstract boolean com.psychology.platform.repository.AppointmentRepository.existsConflictingAppointment(java.lang.Long,java.time.LocalDateTime,java.time.LocalDateTime)
	at org.springframework.data.repository.query.QueryCreationException.create(QueryCreationException.java:101) ~[spring-data-commons-3.2.0.jar:3.2.0]
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:115) ~[spring-data-commons-3.2.0.jar:3.2.0]
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.mapMethodsToQuery(QueryExecutorMethodInterceptor.java:99) ~[spring-data-commons-3.2.0.jar:3.2.0]
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lambda$new$0(QueryExecutorMethodInterceptor.java:88) ~[spring-data-commons-3.2.0.jar:3.2.0]
	at java.base/java.util.Optional.map(Optional.java:260) ~[na:na]
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.<init>(QueryExecutorMethodInterceptor.java:88) ~[spring-data-commons-3.2.0.jar:3.2.0]
	at org.springframework.data.repository.core.support.RepositoryFactorySupport.getRepository(RepositoryFactorySupport.java:357) ~[spring-data-commons-3.2.0.jar:3.2.0]
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.lambda$afterPropertiesSet$5(RepositoryFactoryBeanSupport.java:279) ~[spring-data-commons-3.2.0.jar:3.2.0]
	at org.springframework.data.util.Lazy.getNullable(Lazy.java:135) ~[spring-data-commons-3.2.0.jar:3.2.0]
	at org.springframework.data.util.Lazy.get(Lazy.java:113) ~[spring-data-commons-3.2.0.jar:3.2.0]
	at org.springframework.data.repository.core.support.RepositoryFactoryBeanSupport.afterPropertiesSet(RepositoryFactoryBeanSupport.java:285) ~[spring-data-commons-3.2.0.jar:3.2.0]
	at org.springframework.data.jpa.repository.support.JpaRepositoryFactoryBean.afterPropertiesSet(JpaRepositoryFactoryBean.java:132) ~[spring-data-jpa-3.2.0.jar:3.2.0]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1822) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1771) ~[spring-beans-6.1.1.jar:6.1.1]
	... 47 common frames omitted
Caused by: java.lang.IllegalArgumentException: Validation failed for query for method public abstract boolean com.psychology.platform.repository.AppointmentRepository.existsConflictingAppointment(java.lang.Long,java.time.LocalDateTime,java.time.LocalDateTime)
	at org.springframework.data.jpa.repository.query.SimpleJpaQuery.validateQuery(SimpleJpaQuery.java:100) ~[spring-data-jpa-3.2.0.jar:3.2.0]
	at org.springframework.data.jpa.repository.query.SimpleJpaQuery.<init>(SimpleJpaQuery.java:70) ~[spring-data-jpa-3.2.0.jar:3.2.0]
	at org.springframework.data.jpa.repository.query.JpaQueryFactory.fromMethodWithQueryString(JpaQueryFactory.java:60) ~[spring-data-jpa-3.2.0.jar:3.2.0]
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$DeclaredQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:170) ~[spring-data-jpa-3.2.0.jar:3.2.0]
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$CreateIfNotFoundQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:252) ~[spring-data-jpa-3.2.0.jar:3.2.0]
	at org.springframework.data.jpa.repository.query.JpaQueryLookupStrategy$AbstractQueryLookupStrategy.resolveQuery(JpaQueryLookupStrategy.java:95) ~[spring-data-jpa-3.2.0.jar:3.2.0]
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.lookupQuery(QueryExecutorMethodInterceptor.java:111) ~[spring-data-commons-3.2.0.jar:3.2.0]
	... 59 common frames omitted
Caused by: java.lang.IllegalArgumentException: org.hibernate.query.SyntaxException: At 1:220 and token 'a', no viable alternative at input 'SELECT COUNT(a) > 0 FROM Appointment a WHERE a.counselor.id = :counselorId AND a.status IN ('CONFIRMED', 'IN_PROGRESS') AND ((a.scheduledTime <= :startTime AND :startTime < FUNCTION('DATE_ADD', a.scheduledTime, INTERVAL *a.durationMinutes MINUTE)) OR (a.scheduledTime < :endTime AND :endTime <= FUNCTION('DATE_ADD', a.scheduledTime, INTERVAL a.durationMinutes MINUTE)) OR (:startTime <= a.scheduledTime AND FUNCTION('DATE_ADD', a.scheduledTime, INTERVAL a.durationMinutes MINUTE) <= :endTime))' [SELECT COUNT(a) > 0 FROM Appointment a WHERE a.counselor.id = :counselorId AND a.status IN ('CONFIRMED', 'IN_PROGRESS') AND ((a.scheduledTime <= :startTime AND :startTime < FUNCTION('DATE_ADD', a.scheduledTime, INTERVAL a.durationMinutes MINUTE)) OR (a.scheduledTime < :endTime AND :endTime <= FUNCTION('DATE_ADD', a.scheduledTime, INTERVAL a.durationMinutes MINUTE)) OR (:startTime <= a.scheduledTime AND FUNCTION('DATE_ADD', a.scheduledTime, INTERVAL a.durationMinutes MINUTE) <= :endTime))]
	at org.hibernate.internal.ExceptionConverterImpl.convert(ExceptionConverterImpl.java:143) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.internal.ExceptionConverterImpl.convert(ExceptionConverterImpl.java:167) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.internal.ExceptionConverterImpl.convert(ExceptionConverterImpl.java:173) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.internal.AbstractSharedSessionContract.createQuery(AbstractSharedSessionContract.java:802) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.internal.AbstractSharedSessionContract.createQuery(AbstractSharedSessionContract.java:707) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.internal.AbstractSharedSessionContract.createQuery(AbstractSharedSessionContract.java:132) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:580) ~[na:na]
	at org.springframework.orm.jpa.ExtendedEntityManagerCreator$ExtendedEntityManagerInvocationHandler.invoke(ExtendedEntityManagerCreator.java:360) ~[spring-orm-6.1.1.jar:6.1.1]
	at jdk.proxy4/jdk.proxy4.$Proxy145.createQuery(Unknown Source) ~[na:na]
	at org.springframework.data.jpa.repository.query.SimpleJpaQuery.validateQuery(SimpleJpaQuery.java:94) ~[spring-data-jpa-3.2.0.jar:3.2.0]
	... 65 common frames omitted
Caused by: org.hibernate.query.SyntaxException: At 1:220 and token 'a', no viable alternative at input 'SELECT COUNT(a) > 0 FROM Appointment a WHERE a.counselor.id = :counselorId AND a.status IN ('CONFIRMED', 'IN_PROGRESS') AND ((a.scheduledTime <= :startTime AND :startTime < FUNCTION('DATE_ADD', a.scheduledTime, INTERVAL *a.durationMinutes MINUTE)) OR (a.scheduledTime < :endTime AND :endTime <= FUNCTION('DATE_ADD', a.scheduledTime, INTERVAL a.durationMinutes MINUTE)) OR (:startTime <= a.scheduledTime AND FUNCTION('DATE_ADD', a.scheduledTime, INTERVAL a.durationMinutes MINUTE) <= :endTime))' [SELECT COUNT(a) > 0 FROM Appointment a WHERE a.counselor.id = :counselorId AND a.status IN ('CONFIRMED', 'IN_PROGRESS') AND ((a.scheduledTime <= :startTime AND :startTime < FUNCTION('DATE_ADD', a.scheduledTime, INTERVAL a.durationMinutes MINUTE)) OR (a.scheduledTime < :endTime AND :endTime <= FUNCTION('DATE_ADD', a.scheduledTime, INTERVAL a.durationMinutes MINUTE)) OR (:startTime <= a.scheduledTime AND FUNCTION('DATE_ADD', a.scheduledTime, INTERVAL a.durationMinutes MINUTE) <= :endTime))]
	at org.hibernate.query.hql.internal.StandardHqlTranslator$1.syntaxError(StandardHqlTranslator.java:108) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.antlr.v4.runtime.ProxyErrorListener.syntaxError(ProxyErrorListener.java:41) ~[antlr4-runtime-4.10.1.jar:4.10.1]
	at org.antlr.v4.runtime.Parser.notifyErrorListeners(Parser.java:543) ~[antlr4-runtime-4.10.1.jar:4.10.1]
	at org.antlr.v4.runtime.DefaultErrorStrategy.reportNoViableAlternative(DefaultErrorStrategy.java:310) ~[antlr4-runtime-4.10.1.jar:4.10.1]
	at org.antlr.v4.runtime.DefaultErrorStrategy.reportError(DefaultErrorStrategy.java:136) ~[antlr4-runtime-4.10.1.jar:4.10.1]
	at org.hibernate.grammars.hql.HqlParser.queryExpression(HqlParser.java:1787) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.grammars.hql.HqlParser.selectStatement(HqlParser.java:405) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.grammars.hql.HqlParser.statement(HqlParser.java:336) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.hql.internal.StandardHqlTranslator.parseHql(StandardHqlTranslator.java:132) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.hql.internal.StandardHqlTranslator.translate(StandardHqlTranslator.java:67) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.internal.QueryInterpretationCacheStandardImpl.createHqlInterpretation(QueryInterpretationCacheStandardImpl.java:165) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.query.internal.QueryInterpretationCacheStandardImpl.resolveHqlInterpretation(QueryInterpretationCacheStandardImpl.java:147) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.internal.AbstractSharedSessionContract.interpretHql(AbstractSharedSessionContract.java:744) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	at org.hibernate.internal.AbstractSharedSessionContract.createQuery(AbstractSharedSessionContract.java:794) ~[hibernate-core-6.3.1.Final.jar:6.3.1.Final]
	... 72 common frames omitted

2025-08-31 21:51:05.534  INFO 16816 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : Starting PsychologyPlatformApplication using Java 21.0.8 with PID 16816 (C:\Users\<USER>\Desktop\a\backend\target\classes started by 21653 in C:\Users\<USER>\Desktop\a\backend)
2025-08-31 21:51:05.536  INFO 16816 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : No active profile set, falling back to 1 default profile: "default"
2025-08-31 21:51:05.598  INFO 16816 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-31 21:51:05.598  INFO 16816 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-31 21:51:06.334  INFO 16816 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-31 21:51:06.405  INFO 16816 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 60 ms. Found 4 JPA repository interfaces.
2025-08-31 21:51:07.254  INFO 16816 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-08-31 21:51:07.262  INFO 16816 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-31 21:51:07.263  INFO 16816 --- [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-08-31 21:51:07.309  INFO 16816 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring embedded WebApplicationContext
2025-08-31 21:51:07.309  INFO 16816 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1711 ms
2025-08-31 21:51:07.493  INFO 16816 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-31 21:51:07.557  INFO 16816 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.3.1.Final
2025-08-31 21:51:07.597  INFO 16816 --- [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-31 21:51:07.843  INFO 16816 --- [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-31 21:51:07.865  INFO 16816 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Starting...
2025-08-31 21:51:08.017  INFO 16816 --- [restartedMain] com.zaxxer.hikari.pool.HikariPool        : PsychologyPlatformHikariCP - Added connection com.mysql.cj.jdbc.ConnectionImpl@25d89e5b
2025-08-31 21:51:08.019  INFO 16816 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Start completed.
2025-08-31 21:51:08.057  WARN 16816 --- [restartedMain] org.hibernate.orm.deprecation            : HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-31 21:51:08.972  INFO 16816 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-31 21:51:08.975  INFO 16816 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-31 21:51:09.287  INFO 16816 --- [restartedMain] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-08-31 21:51:10.419  WARN 16816 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-31 21:51:10.759  INFO 16816 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@6d2dc039, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@45bb6b31, org.springframework.security.web.context.SecurityContextHolderFilter@1d03588a, org.springframework.security.web.header.HeaderWriterFilter@445fef67, org.springframework.web.filter.CorsFilter@55a69dbb, org.springframework.security.web.authentication.logout.LogoutFilter@62b0176b, com.psychology.platform.security.JwtAuthenticationFilter@301947a7, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5111332, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@270303d3, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@224775c2, org.springframework.security.web.session.SessionManagementFilter@213e9acb, org.springframework.security.web.access.ExceptionTranslationFilter@5b2cec4d, org.springframework.security.web.access.intercept.AuthorizationFilter@553fa025]
2025-08-31 21:51:11.089  INFO 16816 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-08-31 21:51:11.134  INFO 16816 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/api'
2025-08-31 21:51:11.142  INFO 16816 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : Started PsychologyPlatformApplication in 6.209 seconds (process running for 7.173)
2025-08-31 21:52:10.284  INFO 16816 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-31 21:52:10.285  INFO 16816 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-31 21:52:10.288  INFO 16816 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 3 ms
2025-08-31 21:52:10.309  WARN 16816 --- [http-nio-8080-exec-1] o.s.w.s.h.HandlerMappingIntrospector     : Cache miss for REQUEST dispatch to '/api/counselors/popular' (previous null). Performing MatchableHandlerMapping lookup. This is logged once only at WARN level, and every time at TRACE.
2025-08-31 21:57:56.162  INFO 16816 --- [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-08-31 21:57:56.200  INFO 16816 --- [Thread-5] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-31 21:57:56.203  INFO 16816 --- [Thread-5] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Shutdown initiated...
2025-08-31 21:57:56.212  INFO 16816 --- [Thread-5] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Shutdown completed.
2025-08-31 21:57:56.317  INFO 16816 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : Starting PsychologyPlatformApplication using Java 21.0.8 with PID 16816 (C:\Users\<USER>\Desktop\a\backend\target\classes started by 21653 in C:\Users\<USER>\Desktop\a\backend)
2025-08-31 21:57:56.318  INFO 16816 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : No active profile set, falling back to 1 default profile: "default"
2025-08-31 21:57:56.585  INFO 16816 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-31 21:57:56.601  INFO 16816 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 15 ms. Found 4 JPA repository interfaces.
2025-08-31 21:57:56.893  INFO 16816 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-08-31 21:57:56.895  INFO 16816 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-31 21:57:56.895  INFO 16816 --- [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-08-31 21:57:56.913  INFO 16816 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring embedded WebApplicationContext
2025-08-31 21:57:56.913  INFO 16816 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 592 ms
2025-08-31 21:57:56.971  INFO 16816 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-31 21:57:56.974  INFO 16816 --- [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-31 21:57:56.977  INFO 16816 --- [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-31 21:57:56.980  INFO 16816 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Starting...
2025-08-31 21:57:56.987  INFO 16816 --- [restartedMain] com.zaxxer.hikari.pool.HikariPool        : PsychologyPlatformHikariCP - Added connection com.mysql.cj.jdbc.ConnectionImpl@7c92f8f
2025-08-31 21:57:56.987  INFO 16816 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Start completed.
2025-08-31 21:57:56.988  WARN 16816 --- [restartedMain] org.hibernate.orm.deprecation            : HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-31 21:57:57.102  INFO 16816 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-31 21:57:57.104  INFO 16816 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-31 21:57:57.673  WARN 16816 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-31 21:57:57.828  INFO 16816 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@7c899afc, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2a87c5f9, org.springframework.security.web.context.SecurityContextHolderFilter@220c5f83, org.springframework.security.web.header.HeaderWriterFilter@2899a7fc, org.springframework.web.filter.CorsFilter@3ffbf374, org.springframework.security.web.authentication.logout.LogoutFilter@1598681c, com.psychology.platform.security.JwtAuthenticationFilter@29cfa03f, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@359b422a, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@5058a522, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@190e6605, org.springframework.security.web.session.SessionManagementFilter@7bcfd75b, org.springframework.security.web.access.ExceptionTranslationFilter@4f0b1171, org.springframework.security.web.access.intercept.AuthorizationFilter@6f19295f]
2025-08-31 21:57:58.008  INFO 16816 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-08-31 21:57:58.022  INFO 16816 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/api'
2025-08-31 21:57:58.025  INFO 16816 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : Started PsychologyPlatformApplication in 1.747 seconds (process running for 414.057)
2025-08-31 21:57:58.027  INFO 16816 --- [restartedMain] .ConditionEvaluationDeltaLoggingListener : Condition evaluation unchanged
2025-08-31 21:58:07.328  INFO 16816 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-31 21:58:07.328  INFO 16816 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Shutdown initiated...
2025-08-31 21:58:07.331  INFO 16816 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Shutdown completed.
2025-08-31 21:58:12.311  INFO 13352 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : Starting PsychologyPlatformApplication using Java 21.0.8 with PID 13352 (C:\Users\<USER>\Desktop\a\backend\target\classes started by 21653 in C:\Users\<USER>\Desktop\a\backend)
2025-08-31 21:58:12.313  INFO 13352 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : No active profile set, falling back to 1 default profile: "default"
2025-08-31 21:58:12.391  INFO 13352 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-31 21:58:12.391  INFO 13352 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-31 21:58:13.143  INFO 13352 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-31 21:58:13.197  INFO 13352 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 46 ms. Found 4 JPA repository interfaces.
2025-08-31 21:58:14.099  INFO 13352 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-08-31 21:58:14.113  INFO 13352 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-31 21:58:14.113  INFO 13352 --- [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-08-31 21:58:14.171  INFO 13352 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring embedded WebApplicationContext
2025-08-31 21:58:14.171  INFO 13352 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1779 ms
2025-08-31 21:58:14.330  INFO 13352 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-31 21:58:14.411  INFO 13352 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.3.1.Final
2025-08-31 21:58:14.452  INFO 13352 --- [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-31 21:58:14.700  INFO 13352 --- [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-31 21:58:14.731  INFO 13352 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Starting...
2025-08-31 21:58:14.886  INFO 13352 --- [restartedMain] com.zaxxer.hikari.pool.HikariPool        : PsychologyPlatformHikariCP - Added connection com.mysql.cj.jdbc.ConnectionImpl@4faeb300
2025-08-31 21:58:14.889  INFO 13352 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Start completed.
2025-08-31 21:58:14.947  WARN 13352 --- [restartedMain] org.hibernate.orm.deprecation            : HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-31 21:58:16.018  INFO 13352 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-31 21:58:16.024  INFO 13352 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-31 21:58:16.334  INFO 13352 --- [restartedMain] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-08-31 21:58:18.607  WARN 13352 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-31 21:58:19.401  INFO 13352 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@6b1a9aab, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@446429e7, org.springframework.security.web.context.SecurityContextHolderFilter@2a4c65d1, org.springframework.security.web.header.HeaderWriterFilter@3bd7a3d0, org.springframework.web.filter.CorsFilter@48e66655, org.springframework.security.web.authentication.logout.LogoutFilter@e60c882, com.psychology.platform.security.JwtAuthenticationFilter@522bc592, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@299c6c4e, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@22e63edb, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@2093783f, org.springframework.security.web.session.SessionManagementFilter@1eb9412f, org.springframework.security.web.access.ExceptionTranslationFilter@15a1302e, org.springframework.security.web.access.intercept.AuthorizationFilter@702bff50]
2025-08-31 21:58:19.956  INFO 13352 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-08-31 21:58:20.027  INFO 13352 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/api'
2025-08-31 21:58:20.037  INFO 13352 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : Started PsychologyPlatformApplication in 8.329 seconds (process running for 9.301)
2025-08-31 21:58:35.658  INFO 13352 --- [http-nio-8080-exec-2] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-31 21:58:35.658  INFO 13352 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-31 21:58:35.661  INFO 13352 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-08-31 21:58:35.680  WARN 13352 --- [http-nio-8080-exec-2] o.s.w.s.h.HandlerMappingIntrospector     : Cache miss for REQUEST dispatch to '/api/users/check/username' (previous null). Performing MatchableHandlerMapping lookup. This is logged once only at WARN level, and every time at TRACE.
2025-08-31 21:58:42.206  INFO 13352 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-31 21:58:42.213  INFO 13352 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Shutdown initiated...
2025-08-31 21:58:42.221  INFO 13352 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Shutdown completed.
2025-08-31 21:58:58.275  INFO 15400 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : Starting PsychologyPlatformApplication using Java 21.0.8 with PID 15400 (C:\Users\<USER>\Desktop\a\backend\target\classes started by 21653 in C:\Users\<USER>\Desktop\a\backend)
2025-08-31 21:58:58.278  INFO 15400 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : No active profile set, falling back to 1 default profile: "default"
2025-08-31 21:58:58.339  INFO 15400 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-31 21:58:58.340  INFO 15400 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-31 21:58:59.098  INFO 15400 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-31 21:58:59.146  INFO 15400 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 41 ms. Found 4 JPA repository interfaces.
2025-08-31 21:59:00.032  INFO 15400 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-08-31 21:59:00.046  INFO 15400 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-31 21:59:00.047  INFO 15400 --- [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-08-31 21:59:00.110  INFO 15400 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring embedded WebApplicationContext
2025-08-31 21:59:00.111  INFO 15400 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1770 ms
2025-08-31 21:59:00.262  INFO 15400 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-31 21:59:00.325  INFO 15400 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.3.1.Final
2025-08-31 21:59:00.353  INFO 15400 --- [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-31 21:59:00.565  INFO 15400 --- [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-31 21:59:00.589  INFO 15400 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Starting...
2025-08-31 21:59:00.731  INFO 15400 --- [restartedMain] com.zaxxer.hikari.pool.HikariPool        : PsychologyPlatformHikariCP - Added connection com.mysql.cj.jdbc.ConnectionImpl@3d004bc5
2025-08-31 21:59:00.732  INFO 15400 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Start completed.
2025-08-31 21:59:00.801  WARN 15400 --- [restartedMain] org.hibernate.orm.deprecation            : HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-31 21:59:01.675  INFO 15400 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-31 21:59:01.678  INFO 15400 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-31 21:59:01.958  INFO 15400 --- [restartedMain] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-08-31 21:59:03.059  WARN 15400 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-31 21:59:03.488  INFO 15400 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@6e26f0e9, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2031c6bc, org.springframework.security.web.context.SecurityContextHolderFilter@195cb786, org.springframework.security.web.header.HeaderWriterFilter@38c4b588, org.springframework.web.filter.CorsFilter@6cec7336, org.springframework.security.web.authentication.logout.LogoutFilter@66bef3ac, com.psychology.platform.security.JwtAuthenticationFilter@79179245, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@392c3eea, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4da07bb9, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@bb72c7, org.springframework.security.web.session.SessionManagementFilter@c8323b5, org.springframework.security.web.access.ExceptionTranslationFilter@2aaa81d9, org.springframework.security.web.access.intercept.AuthorizationFilter@3d0e6ae3]
2025-08-31 21:59:03.777  INFO 15400 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-08-31 21:59:03.815  INFO 15400 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/api'
2025-08-31 21:59:03.822  INFO 15400 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : Started PsychologyPlatformApplication in 6.303 seconds (process running for 7.567)
2025-08-31 21:59:10.337  INFO 15400 --- [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-08-31 21:59:10.354  INFO 15400 --- [Thread-5] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-31 21:59:10.360  INFO 15400 --- [Thread-5] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Shutdown initiated...
2025-08-31 21:59:10.372  INFO 15400 --- [Thread-5] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Shutdown completed.
2025-08-31 21:59:10.598  INFO 15400 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : Starting PsychologyPlatformApplication using Java 21.0.8 with PID 15400 (C:\Users\<USER>\Desktop\a\backend\target\classes started by 21653 in C:\Users\<USER>\Desktop\a\backend)
2025-08-31 21:59:10.599  INFO 15400 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : No active profile set, falling back to 1 default profile: "default"
2025-08-31 21:59:10.971  INFO 15400 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-31 21:59:10.994  INFO 15400 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 22 ms. Found 4 JPA repository interfaces.
2025-08-31 21:59:11.296  INFO 15400 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-08-31 21:59:11.298  INFO 15400 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-31 21:59:11.299  INFO 15400 --- [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-08-31 21:59:11.327  INFO 15400 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring embedded WebApplicationContext
2025-08-31 21:59:11.327  INFO 15400 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 725 ms
2025-08-31 21:59:11.425  INFO 15400 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-31 21:59:11.428  INFO 15400 --- [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-31 21:59:11.433  INFO 15400 --- [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-31 21:59:11.436  INFO 15400 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Starting...
2025-08-31 21:59:11.471  INFO 15400 --- [restartedMain] com.zaxxer.hikari.pool.HikariPool        : PsychologyPlatformHikariCP - Added connection com.mysql.cj.jdbc.ConnectionImpl@6f873346
2025-08-31 21:59:11.471  INFO 15400 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Start completed.
2025-08-31 21:59:11.473  WARN 15400 --- [restartedMain] org.hibernate.orm.deprecation            : HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-31 21:59:11.894  INFO 15400 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-31 21:59:11.895  INFO 15400 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-31 21:59:13.397  WARN 15400 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-31 21:59:13.653  INFO 15400 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@5ba86e2e, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@58b9b66a, org.springframework.security.web.context.SecurityContextHolderFilter@3371cf23, org.springframework.security.web.header.HeaderWriterFilter@77d07dc2, org.springframework.web.filter.CorsFilter@2cdf9e5b, org.springframework.security.web.authentication.logout.LogoutFilter@6d9dd1e0, com.psychology.platform.security.JwtAuthenticationFilter@1bfc56df, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5c12a38f, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2858eba2, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@60bd7ede, org.springframework.security.web.session.SessionManagementFilter@99e22ca, org.springframework.security.web.access.ExceptionTranslationFilter@1bdc624, org.springframework.security.web.access.intercept.AuthorizationFilter@4bb0f3c5]
2025-08-31 21:59:13.940  INFO 15400 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-08-31 21:59:13.966  INFO 15400 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/api'
2025-08-31 21:59:13.971  INFO 15400 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : Started PsychologyPlatformApplication in 3.481 seconds (process running for 17.716)
2025-08-31 21:59:13.976  INFO 15400 --- [restartedMain] .ConditionEvaluationDeltaLoggingListener : Condition evaluation unchanged
2025-08-31 21:59:17.390  INFO 15400 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-31 21:59:17.391  INFO 15400 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-31 21:59:17.392  INFO 15400 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-08-31 21:59:17.426  WARN 15400 --- [http-nio-8080-exec-1] o.s.w.s.h.HandlerMappingIntrospector     : Cache miss for REQUEST dispatch to '/api/users/check/username' (previous null). Performing MatchableHandlerMapping lookup. This is logged once only at WARN level, and every time at TRACE.
2025-08-31 22:00:07.202  INFO 15400 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-31 22:00:07.203  INFO 15400 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Shutdown initiated...
2025-08-31 22:00:07.207  INFO 15400 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Shutdown completed.
2025-08-31 22:00:09.631  INFO 18016 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : Starting PsychologyPlatformApplication using Java 21.0.8 with PID 18016 (C:\Users\<USER>\Desktop\a\backend\target\classes started by 21653 in C:\Users\<USER>\Desktop\a\backend)
2025-08-31 22:00:09.635  INFO 18016 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : No active profile set, falling back to 1 default profile: "default"
2025-08-31 22:00:09.697  INFO 18016 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-31 22:00:09.699  INFO 18016 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-31 22:00:10.561  INFO 18016 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-31 22:00:10.617  INFO 18016 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 48 ms. Found 4 JPA repository interfaces.
2025-08-31 22:00:11.497  INFO 18016 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-08-31 22:00:11.506  INFO 18016 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-31 22:00:11.506  INFO 18016 --- [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-08-31 22:00:11.554  INFO 18016 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring embedded WebApplicationContext
2025-08-31 22:00:11.554  INFO 18016 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1854 ms
2025-08-31 22:00:11.682  INFO 18016 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-31 22:00:11.745  INFO 18016 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.3.1.Final
2025-08-31 22:00:11.788  INFO 18016 --- [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-31 22:00:12.010  INFO 18016 --- [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-31 22:00:12.032  INFO 18016 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Starting...
2025-08-31 22:00:12.181  INFO 18016 --- [restartedMain] com.zaxxer.hikari.pool.HikariPool        : PsychologyPlatformHikariCP - Added connection com.mysql.cj.jdbc.ConnectionImpl@1b67c37e
2025-08-31 22:00:12.183  INFO 18016 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Start completed.
2025-08-31 22:00:12.217  WARN 18016 --- [restartedMain] org.hibernate.orm.deprecation            : HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-31 22:00:13.224  INFO 18016 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-31 22:00:13.227  INFO 18016 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-31 22:00:13.520  INFO 18016 --- [restartedMain] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-08-31 22:00:14.575  WARN 18016 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-31 22:00:15.033  INFO 18016 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@2f2e815e, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@223f2dbc, org.springframework.security.web.context.SecurityContextHolderFilter@62a25256, org.springframework.security.web.header.HeaderWriterFilter@3f73a873, org.springframework.web.filter.CorsFilter@32a680ae, org.springframework.security.web.authentication.logout.LogoutFilter@55a69dbb, com.psychology.platform.security.JwtAuthenticationFilter@1ec688, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@580df1a, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4073eff1, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1c2c4b2d, org.springframework.security.web.session.SessionManagementFilter@5f461fa3, org.springframework.security.web.access.ExceptionTranslationFilter@213e9acb, org.springframework.security.web.access.intercept.AuthorizationFilter@24353522]
2025-08-31 22:00:15.477  INFO 18016 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-08-31 22:00:15.531  INFO 18016 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/api'
2025-08-31 22:00:15.551  INFO 18016 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : Started PsychologyPlatformApplication in 6.561 seconds (process running for 7.618)
2025-08-31 22:00:22.996  INFO 18016 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-31 22:00:22.996  INFO 18016 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-31 22:00:22.998  INFO 18016 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-08-31 22:00:23.016  WARN 18016 --- [http-nio-8080-exec-1] o.s.w.s.h.HandlerMappingIntrospector     : Cache miss for REQUEST dispatch to '/api/users/check/username' (previous null). Performing MatchableHandlerMapping lookup. This is logged once only at WARN level, and every time at TRACE.
2025-08-31 22:01:43.143  INFO 18016 --- [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-08-31 22:01:43.211  INFO 18016 --- [Thread-5] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-31 22:01:43.218  INFO 18016 --- [Thread-5] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Shutdown initiated...
2025-08-31 22:01:43.261  INFO 18016 --- [Thread-5] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Shutdown completed.
2025-08-31 22:01:43.438  INFO 18016 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : Starting PsychologyPlatformApplication using Java 21.0.8 with PID 18016 (C:\Users\<USER>\Desktop\a\backend\target\classes started by 21653 in C:\Users\<USER>\Desktop\a\backend)
2025-08-31 22:01:43.438  INFO 18016 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : No active profile set, falling back to 1 default profile: "default"
2025-08-31 22:01:43.694  INFO 18016 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-31 22:01:43.715  INFO 18016 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 20 ms. Found 4 JPA repository interfaces.
2025-08-31 22:01:43.993  INFO 18016 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-08-31 22:01:43.995  INFO 18016 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-31 22:01:43.995  INFO 18016 --- [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-08-31 22:01:44.022  INFO 18016 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring embedded WebApplicationContext
2025-08-31 22:01:44.023  INFO 18016 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 582 ms
2025-08-31 22:01:44.083  INFO 18016 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-31 22:01:44.087  INFO 18016 --- [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-31 22:01:44.092  INFO 18016 --- [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-31 22:01:44.096  INFO 18016 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Starting...
2025-08-31 22:01:44.101  INFO 18016 --- [restartedMain] com.zaxxer.hikari.pool.HikariPool        : PsychologyPlatformHikariCP - Added connection com.mysql.cj.jdbc.ConnectionImpl@49d3a481
2025-08-31 22:01:44.101  INFO 18016 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Start completed.
2025-08-31 22:01:44.103  WARN 18016 --- [restartedMain] org.hibernate.orm.deprecation            : HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-31 22:01:44.311  INFO 18016 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-31 22:01:44.312  INFO 18016 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-31 22:01:45.345  WARN 18016 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-31 22:01:45.687  INFO 18016 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@71d8e09, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@4d717fad, org.springframework.security.web.context.SecurityContextHolderFilter@1cbfa02e, org.springframework.security.web.header.HeaderWriterFilter@3a0c8216, org.springframework.web.filter.CorsFilter@2c8dffc8, org.springframework.security.web.authentication.logout.LogoutFilter@189a5790, com.psychology.platform.security.JwtAuthenticationFilter@5a7c5cca, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5cdbd793, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@299f22c, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@2c9a7872, org.springframework.security.web.session.SessionManagementFilter@3639a3ca, org.springframework.security.web.access.ExceptionTranslationFilter@5c2390ea, org.springframework.security.web.access.intercept.AuthorizationFilter@57baf5ae]
2025-08-31 22:01:45.865  INFO 18016 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-08-31 22:01:45.894  INFO 18016 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/api'
2025-08-31 22:01:45.901  INFO 18016 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : Started PsychologyPlatformApplication in 2.522 seconds (process running for 97.968)
2025-08-31 22:01:45.903  INFO 18016 --- [restartedMain] .ConditionEvaluationDeltaLoggingListener : Condition evaluation unchanged
2025-08-31 22:02:06.477  INFO 18016 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-31 22:02:06.479  INFO 18016 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Shutdown initiated...
2025-08-31 22:02:06.508  INFO 18016 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Shutdown completed.
2025-08-31 22:02:12.980  INFO 8200 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : Starting PsychologyPlatformApplication using Java 21.0.8 with PID 8200 (C:\Users\<USER>\Desktop\a\backend\target\classes started by 21653 in C:\Users\<USER>\Desktop\a\backend)
2025-08-31 22:02:12.983  INFO 8200 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : No active profile set, falling back to 1 default profile: "default"
2025-08-31 22:02:13.040  INFO 8200 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-31 22:02:13.040  INFO 8200 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-31 22:02:13.728  INFO 8200 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-31 22:02:13.776  INFO 8200 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 40 ms. Found 4 JPA repository interfaces.
2025-08-31 22:02:14.672  INFO 8200 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-08-31 22:02:14.680  INFO 8200 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-31 22:02:14.680  INFO 8200 --- [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-08-31 22:02:14.726  INFO 8200 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring embedded WebApplicationContext
2025-08-31 22:02:14.726  INFO 8200 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1685 ms
2025-08-31 22:02:14.857  INFO 8200 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-31 22:02:14.916  INFO 8200 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.3.1.Final
2025-08-31 22:02:14.954  INFO 8200 --- [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-31 22:02:15.160  INFO 8200 --- [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-31 22:02:15.184  INFO 8200 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Starting...
2025-08-31 22:02:15.365  INFO 8200 --- [restartedMain] com.zaxxer.hikari.pool.HikariPool        : PsychologyPlatformHikariCP - Added connection com.mysql.cj.jdbc.ConnectionImpl@283433e3
2025-08-31 22:02:15.367  INFO 8200 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Start completed.
2025-08-31 22:02:15.418  WARN 8200 --- [restartedMain] org.hibernate.orm.deprecation            : HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-31 22:02:16.338  INFO 8200 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-31 22:02:16.341  INFO 8200 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-31 22:02:16.640  INFO 8200 --- [restartedMain] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-08-31 22:02:17.851  WARN 8200 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-31 22:02:18.353  INFO 8200 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@29d9b156, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@7dcba07, org.springframework.security.web.context.SecurityContextHolderFilter@531eb2e4, org.springframework.security.web.header.HeaderWriterFilter@56a4e180, org.springframework.web.filter.CorsFilter@7280ac4e, org.springframework.security.web.authentication.logout.LogoutFilter@8f6121e, com.psychology.platform.security.JwtAuthenticationFilter@13e993d9, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@38ca0208, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@3e3efe04, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@592a5309, org.springframework.security.web.session.SessionManagementFilter@3d896143, org.springframework.security.web.access.ExceptionTranslationFilter@4e72e75b, org.springframework.security.web.access.intercept.AuthorizationFilter@45ddcf]
2025-08-31 22:02:18.701  INFO 8200 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-08-31 22:02:18.743  INFO 8200 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/api'
2025-08-31 22:02:18.750  INFO 8200 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : Started PsychologyPlatformApplication in 6.428 seconds (process running for 7.248)
2025-08-31 22:02:26.316  INFO 8200 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-31 22:02:26.316  INFO 8200 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-31 22:02:26.317  INFO 8200 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-08-31 22:02:26.345  WARN 8200 --- [http-nio-8080-exec-1] o.s.w.s.h.HandlerMappingIntrospector     : Cache miss for REQUEST dispatch to '/api/users/check/username' (previous null). Performing MatchableHandlerMapping lookup. This is logged once only at WARN level, and every time at TRACE.
2025-08-31 22:02:41.369  WARN 8200 --- [http-nio-8080-exec-4] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 1054, SQLState: 42S22
2025-08-31 22:02:41.370 ERROR 8200 --- [http-nio-8080-exec-4] o.h.engine.jdbc.spi.SqlExceptionHelper   : Unknown column 'deleted' in 'field list'
2025-08-31 22:02:41.409  WARN 8200 --- [http-nio-8080-exec-4] .m.m.a.ExceptionHandlerExceptionResolver : Resolved [org.springframework.dao.InvalidDataAccessResourceUsageException: could not execute statement [Unknown column 'deleted' in 'field list'] [/* insert for com.psychology.platform.entity.User */insert into users (avatar_url,bio,birth_date,created_at,deleted,email,email_verified,gender,last_login_time,password,phone,phone_verified,real_name,status,updated_at,username) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; SQL [/* insert for com.psychology.platform.entity.User */insert into users (avatar_url,bio,birth_date,created_at,deleted,email,email_verified,gender,last_login_time,password,phone,phone_verified,real_name,status,updated_at,username) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]]
2025-08-31 22:08:03.689  WARN 8200 --- [http-nio-8080-exec-9] .m.m.a.ExceptionHandlerExceptionResolver : Resolved [java.lang.RuntimeException: 用户不存在]
2025-08-31 22:08:25.548  WARN 8200 --- [http-nio-8080-exec-10] .m.m.a.ExceptionHandlerExceptionResolver : Resolved [java.lang.RuntimeException: 用户不存在]
2025-08-31 22:09:02.348  WARN 8200 --- [http-nio-8080-exec-1] .m.m.a.ExceptionHandlerExceptionResolver : Resolved [java.lang.RuntimeException: 用户不存在]
2025-08-31 22:09:53.528  INFO 8200 --- [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 8 class path changes (0 additions, 0 deletions, 8 modifications)
2025-08-31 22:09:53.536  INFO 8200 --- [Thread-5] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-31 22:09:53.539  INFO 8200 --- [Thread-5] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Shutdown initiated...
2025-08-31 22:09:53.546  INFO 8200 --- [Thread-5] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Shutdown completed.
2025-08-31 22:09:53.653  INFO 8200 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : Starting PsychologyPlatformApplication using Java 21.0.8 with PID 8200 (C:\Users\<USER>\Desktop\a\backend\target\classes started by 21653 in C:\Users\<USER>\Desktop\a\backend)
2025-08-31 22:09:53.653  INFO 8200 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : No active profile set, falling back to 1 default profile: "default"
2025-08-31 22:09:53.864  INFO 8200 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-31 22:09:53.879  INFO 8200 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 14 ms. Found 4 JPA repository interfaces.
2025-08-31 22:09:54.124  INFO 8200 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-08-31 22:09:54.124  INFO 8200 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-31 22:09:54.125  INFO 8200 --- [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-08-31 22:09:54.140  INFO 8200 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring embedded WebApplicationContext
2025-08-31 22:09:54.141  INFO 8200 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 483 ms
2025-08-31 22:09:54.192  INFO 8200 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-31 22:09:54.194  INFO 8200 --- [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-31 22:09:54.198  INFO 8200 --- [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-31 22:09:54.199  INFO 8200 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Starting...
2025-08-31 22:09:54.204  INFO 8200 --- [restartedMain] com.zaxxer.hikari.pool.HikariPool        : PsychologyPlatformHikariCP - Added connection com.mysql.cj.jdbc.ConnectionImpl@46aae9a5
2025-08-31 22:09:54.204  INFO 8200 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Start completed.
2025-08-31 22:09:54.207  WARN 8200 --- [restartedMain] org.hibernate.orm.deprecation            : HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-31 22:09:54.327  INFO 8200 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-31 22:09:54.327  INFO 8200 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-31 22:09:54.672  WARN 8200 --- [restartedMain] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'userController' defined in file [C:\Users\<USER>\Desktop\a\backend\target\classes\com\psychology\platform\controller\UserController.class]: Failed to instantiate [com.psychology.platform.controller.UserController]: Constructor threw exception
2025-08-31 22:09:54.673  INFO 8200 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-31 22:09:54.673  INFO 8200 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Shutdown initiated...
2025-08-31 22:09:54.675  INFO 8200 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Shutdown completed.
2025-08-31 22:09:54.675  INFO 8200 --- [restartedMain] o.apache.catalina.core.StandardService   : Stopping service [Tomcat]
2025-08-31 22:09:54.680  INFO 8200 --- [restartedMain] .s.b.a.l.ConditionEvaluationReportLogger : 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-31 22:09:54.695 ERROR 8200 --- [restartedMain] o.s.boot.SpringApplication               : Application run failed

org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'userController' defined in file [C:\Users\<USER>\Desktop\a\backend\target\classes\com\psychology\platform\controller\UserController.class]: Failed to instantiate [com.psychology.platform.controller.UserController]: Constructor threw exception
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateBean(AbstractAutowireCapableBeanFactory.java:1318) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1203) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:973) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:946) ~[spring-context-6.1.1.jar:6.1.1]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:616) ~[spring-context-6.1.1.jar:6.1.1]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146) ~[spring-boot-3.2.0.jar:3.2.0]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:753) ~[spring-boot-3.2.0.jar:3.2.0]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:455) ~[spring-boot-3.2.0.jar:3.2.0]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:323) ~[spring-boot-3.2.0.jar:3.2.0]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1342) ~[spring-boot-3.2.0.jar:3.2.0]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1331) ~[spring-boot-3.2.0.jar:3.2.0]
	at com.psychology.platform.PsychologyPlatformApplication.main(PsychologyPlatformApplication.java:17) ~[classes/:na]
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:580) ~[na:na]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) ~[spring-boot-devtools-3.2.0.jar:3.2.0]
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [com.psychology.platform.controller.UserController]: Constructor threw exception
	at org.springframework.beans.BeanUtils.instantiateClass(BeanUtils.java:223) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:88) ~[spring-beans-6.1.1.jar:6.1.1]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateBean(AbstractAutowireCapableBeanFactory.java:1312) ~[spring-beans-6.1.1.jar:6.1.1]
	... 20 common frames omitted
Caused by: java.lang.Error: Unresolved compilation problems: 
	JwtTokenProvider cannot be resolved to a type
	AuthenticationManager cannot be resolved to a type
	LoginResponse cannot be resolved to a type
	Authentication cannot be resolved to a type
	AuthenticationManager cannot be resolved to a type
	UsernamePasswordAuthenticationToken cannot be resolved to a type
	JwtTokenProvider cannot be resolved to a type
	LoginResponse cannot be resolved to a type
	LoginResponse cannot be resolved to a type

	at com.psychology.platform.controller.UserController.<init>(UserController.java:34) ~[classes/:na]
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62) ~[na:na]
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502) ~[na:na]
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486) ~[na:na]
	at org.springframework.beans.BeanUtils.instantiateClass(BeanUtils.java:197) ~[spring-beans-6.1.1.jar:6.1.1]
	... 22 common frames omitted

2025-08-31 22:10:12.746  INFO 8200 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : Starting PsychologyPlatformApplication using Java 21.0.8 with PID 8200 (C:\Users\<USER>\Desktop\a\backend\target\classes started by 21653 in C:\Users\<USER>\Desktop\a\backend)
2025-08-31 22:10:12.746  INFO 8200 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : No active profile set, falling back to 1 default profile: "default"
2025-08-31 22:10:12.907  INFO 8200 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-31 22:10:12.924  INFO 8200 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 17 ms. Found 4 JPA repository interfaces.
2025-08-31 22:10:13.113  INFO 8200 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-08-31 22:10:13.115  INFO 8200 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-31 22:10:13.115  INFO 8200 --- [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-08-31 22:10:13.147  INFO 8200 --- [restartedMain] o.a.c.c.C.[Tomcat-1].[localhost].[/api]  : Initializing Spring embedded WebApplicationContext
2025-08-31 22:10:13.147  INFO 8200 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 399 ms
2025-08-31 22:10:13.185  INFO 8200 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-31 22:10:13.189  INFO 8200 --- [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-31 22:10:13.193  INFO 8200 --- [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-31 22:10:13.195  INFO 8200 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Starting...
2025-08-31 22:10:13.198  INFO 8200 --- [restartedMain] com.zaxxer.hikari.pool.HikariPool        : PsychologyPlatformHikariCP - Added connection com.mysql.cj.jdbc.ConnectionImpl@7a1d4001
2025-08-31 22:10:13.199  INFO 8200 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Start completed.
2025-08-31 22:10:13.200  WARN 8200 --- [restartedMain] org.hibernate.orm.deprecation            : HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-31 22:10:13.294  INFO 8200 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-31 22:10:13.294  INFO 8200 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-31 22:10:13.777  WARN 8200 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-31 22:10:13.952  INFO 8200 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@70714176, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@1a695ce7, org.springframework.security.web.context.SecurityContextHolderFilter@498bad0a, org.springframework.security.web.header.HeaderWriterFilter@60a75613, org.springframework.web.filter.CorsFilter@6aba940f, org.springframework.security.web.authentication.logout.LogoutFilter@27d01ee, com.psychology.platform.security.JwtAuthenticationFilter@567140a7, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@195c7f5b, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4391b5ad, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@43319d47, org.springframework.security.web.session.SessionManagementFilter@3c7d3669, org.springframework.security.web.access.ExceptionTranslationFilter@41e23cbc, org.springframework.security.web.access.intercept.AuthorizationFilter@3653ee39]
2025-08-31 22:10:14.127  INFO 8200 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-08-31 22:10:14.144  INFO 8200 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/api'
2025-08-31 22:10:14.148  INFO 8200 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : Started PsychologyPlatformApplication in 1.43 seconds (process running for 482.645)
2025-08-31 22:10:14.150  INFO 8200 --- [restartedMain] .ConditionEvaluationDeltaLoggingListener : Condition evaluation unchanged
2025-08-31 22:10:30.004  INFO 8200 --- [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 9 class path changes (1 addition, 0 deletions, 8 modifications)
2025-08-31 22:10:30.011  INFO 8200 --- [Thread-7] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-31 22:10:30.012  INFO 8200 --- [Thread-7] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Shutdown initiated...
2025-08-31 22:10:30.014  INFO 8200 --- [Thread-7] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Shutdown completed.
2025-08-31 22:10:30.103  INFO 8200 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : Starting PsychologyPlatformApplication using Java 21.0.8 with PID 8200 (C:\Users\<USER>\Desktop\a\backend\target\classes started by 21653 in C:\Users\<USER>\Desktop\a\backend)
2025-08-31 22:10:30.103  INFO 8200 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : No active profile set, falling back to 1 default profile: "default"
2025-08-31 22:10:30.257  INFO 8200 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-31 22:10:30.273  INFO 8200 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 15 ms. Found 4 JPA repository interfaces.
2025-08-31 22:10:30.494  INFO 8200 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-08-31 22:10:30.495  INFO 8200 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-31 22:10:30.495  INFO 8200 --- [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-08-31 22:10:30.513  INFO 8200 --- [restartedMain] o.a.c.c.C.[Tomcat-1].[localhost].[/api]  : Initializing Spring embedded WebApplicationContext
2025-08-31 22:10:30.513  INFO 8200 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 409 ms
2025-08-31 22:10:30.569  INFO 8200 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-31 22:10:30.570  INFO 8200 --- [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-31 22:10:30.575  INFO 8200 --- [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-31 22:10:30.576  INFO 8200 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Starting...
2025-08-31 22:10:30.582  INFO 8200 --- [restartedMain] com.zaxxer.hikari.pool.HikariPool        : PsychologyPlatformHikariCP - Added connection com.mysql.cj.jdbc.ConnectionImpl@6834f5c2
2025-08-31 22:10:30.582  INFO 8200 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Start completed.
2025-08-31 22:10:30.583  WARN 8200 --- [restartedMain] org.hibernate.orm.deprecation            : HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-31 22:10:30.693  INFO 8200 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-31 22:10:30.693  INFO 8200 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-31 22:10:31.561  WARN 8200 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-31 22:10:31.746  INFO 8200 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@636c5408, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@6ce5e723, org.springframework.security.web.context.SecurityContextHolderFilter@6cd1712d, org.springframework.security.web.header.HeaderWriterFilter@4fe2e061, org.springframework.web.filter.CorsFilter@36ce8ac5, org.springframework.security.web.authentication.logout.LogoutFilter@618dc17f, com.psychology.platform.security.JwtAuthenticationFilter@3d60c628, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@204922ff, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@78f03f7f, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@39556fc, org.springframework.security.web.session.SessionManagementFilter@265b1ce0, org.springframework.security.web.access.ExceptionTranslationFilter@5c820ad2, org.springframework.security.web.access.intercept.AuthorizationFilter@714894d4]
2025-08-31 22:10:31.895  INFO 8200 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-08-31 22:10:31.912  INFO 8200 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/api'
2025-08-31 22:10:31.914  INFO 8200 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : Started PsychologyPlatformApplication in 1.839 seconds (process running for 500.412)
2025-08-31 22:10:31.916  INFO 8200 --- [restartedMain] .ConditionEvaluationDeltaLoggingListener : Condition evaluation unchanged
2025-08-31 22:10:44.636  INFO 8200 --- [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-08-31 22:10:44.643  INFO 8200 --- [Thread-14] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-31 22:10:44.644  INFO 8200 --- [Thread-14] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Shutdown initiated...
2025-08-31 22:10:44.647  INFO 8200 --- [Thread-14] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Shutdown completed.
2025-08-31 22:10:44.757  INFO 8200 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : Starting PsychologyPlatformApplication using Java 21.0.8 with PID 8200 (C:\Users\<USER>\Desktop\a\backend\target\classes started by 21653 in C:\Users\<USER>\Desktop\a\backend)
2025-08-31 22:10:44.757  INFO 8200 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : No active profile set, falling back to 1 default profile: "default"
2025-08-31 22:10:44.964  INFO 8200 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-31 22:10:44.984  INFO 8200 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 19 ms. Found 4 JPA repository interfaces.
2025-08-31 22:10:45.249  INFO 8200 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-08-31 22:10:45.249  INFO 8200 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-31 22:10:45.249  INFO 8200 --- [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-08-31 22:10:45.273  INFO 8200 --- [restartedMain] o.a.c.c.C.[Tomcat-1].[localhost].[/api]  : Initializing Spring embedded WebApplicationContext
2025-08-31 22:10:45.273  INFO 8200 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 515 ms
2025-08-31 22:10:45.338  INFO 8200 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-31 22:10:45.342  INFO 8200 --- [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-31 22:10:45.348  INFO 8200 --- [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-31 22:10:45.349  INFO 8200 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Starting...
2025-08-31 22:10:45.355  INFO 8200 --- [restartedMain] com.zaxxer.hikari.pool.HikariPool        : PsychologyPlatformHikariCP - Added connection com.mysql.cj.jdbc.ConnectionImpl@7a5e8f4d
2025-08-31 22:10:45.355  INFO 8200 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Start completed.
2025-08-31 22:10:45.357  WARN 8200 --- [restartedMain] org.hibernate.orm.deprecation            : HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-31 22:10:45.481  INFO 8200 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-31 22:10:45.481  INFO 8200 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-31 22:10:46.384  WARN 8200 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-31 22:10:46.621  INFO 8200 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@503075f9, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@21c89603, org.springframework.security.web.context.SecurityContextHolderFilter@5f5e4dca, org.springframework.security.web.header.HeaderWriterFilter@2ceba579, org.springframework.web.filter.CorsFilter@60faa807, org.springframework.security.web.authentication.logout.LogoutFilter@190d108c, com.psychology.platform.security.JwtAuthenticationFilter@1236a73f, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@636b8cad, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@125a2dd4, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5fad58f6, org.springframework.security.web.session.SessionManagementFilter@6a2998ae, org.springframework.security.web.access.ExceptionTranslationFilter@1d7abdf, org.springframework.security.web.access.intercept.AuthorizationFilter@654b447a]
2025-08-31 22:10:46.843  INFO 8200 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-08-31 22:10:46.865  INFO 8200 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/api'
2025-08-31 22:10:46.869  INFO 8200 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : Started PsychologyPlatformApplication in 2.148 seconds (process running for 515.367)
2025-08-31 22:10:46.872  INFO 8200 --- [restartedMain] .ConditionEvaluationDeltaLoggingListener : Condition evaluation unchanged
2025-08-31 22:11:24.625  INFO 8200 --- [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-08-31 22:11:24.631  INFO 8200 --- [Thread-18] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-31 22:11:24.632  INFO 8200 --- [Thread-18] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Shutdown initiated...
2025-08-31 22:11:24.633  INFO 8200 --- [Thread-18] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Shutdown completed.
2025-08-31 22:11:24.726  INFO 8200 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : Starting PsychologyPlatformApplication using Java 21.0.8 with PID 8200 (C:\Users\<USER>\Desktop\a\backend\target\classes started by 21653 in C:\Users\<USER>\Desktop\a\backend)
2025-08-31 22:11:24.726  INFO 8200 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : No active profile set, falling back to 1 default profile: "default"
2025-08-31 22:11:24.929  INFO 8200 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-31 22:11:24.945  INFO 8200 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 15 ms. Found 4 JPA repository interfaces.
2025-08-31 22:11:25.194  INFO 8200 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-08-31 22:11:25.195  INFO 8200 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-31 22:11:25.195  INFO 8200 --- [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-08-31 22:11:25.215  INFO 8200 --- [restartedMain] o.a.c.c.C.[Tomcat-1].[localhost].[/api]  : Initializing Spring embedded WebApplicationContext
2025-08-31 22:11:25.216  INFO 8200 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 488 ms
2025-08-31 22:11:25.286  INFO 8200 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-31 22:11:25.289  INFO 8200 --- [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-31 22:11:25.294  INFO 8200 --- [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-31 22:11:25.295  INFO 8200 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Starting...
2025-08-31 22:11:25.300  INFO 8200 --- [restartedMain] com.zaxxer.hikari.pool.HikariPool        : PsychologyPlatformHikariCP - Added connection com.mysql.cj.jdbc.ConnectionImpl@131e6c68
2025-08-31 22:11:25.300  INFO 8200 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Start completed.
2025-08-31 22:11:25.301  WARN 8200 --- [restartedMain] org.hibernate.orm.deprecation            : HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-31 22:11:25.417  INFO 8200 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-31 22:11:25.417  INFO 8200 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-31 22:11:26.035  WARN 8200 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-31 22:11:26.225  INFO 8200 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@2f6c3a3d, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@c7cb583, org.springframework.security.web.context.SecurityContextHolderFilter@2787ee97, org.springframework.security.web.header.HeaderWriterFilter@62cc6700, org.springframework.web.filter.CorsFilter@573beae, org.springframework.security.web.authentication.logout.LogoutFilter@6478f97a, com.psychology.platform.security.JwtAuthenticationFilter@5035ef0c, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@3b2d3c1f, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@3ad8b81d, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@49aa6175, org.springframework.security.web.session.SessionManagementFilter@6a05911e, org.springframework.security.web.access.ExceptionTranslationFilter@2d363008, org.springframework.security.web.access.intercept.AuthorizationFilter@4900a2af]
2025-08-31 22:11:26.395  INFO 8200 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-08-31 22:11:26.412  INFO 8200 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/api'
2025-08-31 22:11:26.415  INFO 8200 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : Started PsychologyPlatformApplication in 1.719 seconds (process running for 554.913)
2025-08-31 22:11:26.418  INFO 8200 --- [restartedMain] .ConditionEvaluationDeltaLoggingListener : Condition evaluation unchanged
2025-08-31 22:12:04.348  INFO 8200 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-31 22:12:04.349  INFO 8200 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Shutdown initiated...
2025-08-31 22:12:04.350  INFO 8200 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Shutdown completed.
2025-08-31 22:12:12.172  INFO 9428 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : Starting PsychologyPlatformApplication using Java 21.0.8 with PID 9428 (C:\Users\<USER>\Desktop\a\backend\target\classes started by 21653 in C:\Users\<USER>\Desktop\a\backend)
2025-08-31 22:12:12.174  INFO 9428 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : No active profile set, falling back to 1 default profile: "default"
2025-08-31 22:12:12.229  INFO 9428 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-31 22:12:12.230  INFO 9428 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-31 22:12:13.020  INFO 9428 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-31 22:12:13.070  INFO 9428 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 43 ms. Found 4 JPA repository interfaces.
2025-08-31 22:12:13.876  INFO 9428 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-08-31 22:12:13.885  INFO 9428 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-31 22:12:13.886  INFO 9428 --- [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-08-31 22:12:13.928  INFO 9428 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring embedded WebApplicationContext
2025-08-31 22:12:13.928  INFO 9428 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1698 ms
2025-08-31 22:12:14.067  INFO 9428 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-31 22:12:14.133  INFO 9428 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.3.1.Final
2025-08-31 22:12:14.164  INFO 9428 --- [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-31 22:12:14.362  INFO 9428 --- [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-31 22:12:14.387  INFO 9428 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Starting...
2025-08-31 22:12:14.530  INFO 9428 --- [restartedMain] com.zaxxer.hikari.pool.HikariPool        : PsychologyPlatformHikariCP - Added connection com.mysql.cj.jdbc.ConnectionImpl@270c1b18
2025-08-31 22:12:14.533  INFO 9428 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Start completed.
2025-08-31 22:12:14.569  WARN 9428 --- [restartedMain] org.hibernate.orm.deprecation            : HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-31 22:12:15.523  INFO 9428 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-31 22:12:15.527  INFO 9428 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-31 22:12:15.822  INFO 9428 --- [restartedMain] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-08-31 22:12:17.002  WARN 9428 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-31 22:12:17.354  INFO 9428 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@223f2dbc, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@32a680ae, org.springframework.security.web.context.SecurityContextHolderFilter@d76f2a6, org.springframework.security.web.header.HeaderWriterFilter@1d03588a, org.springframework.web.filter.CorsFilter@1c2c4b2d, org.springframework.security.web.authentication.logout.LogoutFilter@224775c2, com.psychology.platform.security.JwtAuthenticationFilter@130b13d, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@4073eff1, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@30b3354b, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@47d31d87, org.springframework.security.web.session.SessionManagementFilter@4ae842d0, org.springframework.security.web.access.ExceptionTranslationFilter@3a6ef18f, org.springframework.security.web.access.intercept.AuthorizationFilter@44ca8875]
2025-08-31 22:12:17.747  INFO 9428 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-08-31 22:12:17.785  INFO 9428 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/api'
2025-08-31 22:12:17.792  INFO 9428 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : Started PsychologyPlatformApplication in 6.193 seconds (process running for 7.082)
2025-08-31 22:12:29.676  INFO 9428 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-31 22:12:29.678  INFO 9428 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-31 22:12:29.679  INFO 9428 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-08-31 22:12:29.718  WARN 9428 --- [http-nio-8080-exec-1] o.s.w.s.h.HandlerMappingIntrospector     : Cache miss for REQUEST dispatch to '/api/users/login' (previous null). Performing MatchableHandlerMapping lookup. This is logged once only at WARN level, and every time at TRACE.
2025-08-31 22:12:30.159  WARN 9428 --- [http-nio-8080-exec-1] .m.m.a.ExceptionHandlerExceptionResolver : Resolved [org.springframework.security.authentication.DisabledException: User is disabled]
2025-08-31 22:13:49.473  INFO 9428 --- [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 9 class path changes (0 additions, 0 deletions, 9 modifications)
2025-08-31 22:13:49.493  INFO 9428 --- [Thread-5] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-31 22:13:49.499  INFO 9428 --- [Thread-5] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Shutdown initiated...
2025-08-31 22:13:49.509  INFO 9428 --- [Thread-5] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Shutdown completed.
2025-08-31 22:13:49.647  INFO 9428 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : Starting PsychologyPlatformApplication using Java 21.0.8 with PID 9428 (C:\Users\<USER>\Desktop\a\backend\target\classes started by 21653 in C:\Users\<USER>\Desktop\a\backend)
2025-08-31 22:13:49.647  INFO 9428 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : No active profile set, falling back to 1 default profile: "default"
2025-08-31 22:13:49.885  INFO 9428 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-31 22:13:49.898  INFO 9428 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 12 ms. Found 4 JPA repository interfaces.
2025-08-31 22:13:50.113  INFO 9428 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-08-31 22:13:50.114  INFO 9428 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-31 22:13:50.114  INFO 9428 --- [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-08-31 22:13:50.134  INFO 9428 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring embedded WebApplicationContext
2025-08-31 22:13:50.134  INFO 9428 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 484 ms
2025-08-31 22:13:50.197  INFO 9428 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-31 22:13:50.199  INFO 9428 --- [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-31 22:13:50.203  INFO 9428 --- [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-31 22:13:50.204  INFO 9428 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Starting...
2025-08-31 22:13:50.209  INFO 9428 --- [restartedMain] com.zaxxer.hikari.pool.HikariPool        : PsychologyPlatformHikariCP - Added connection com.mysql.cj.jdbc.ConnectionImpl@4b5f3670
2025-08-31 22:13:50.210  INFO 9428 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Start completed.
2025-08-31 22:13:50.211  WARN 9428 --- [restartedMain] org.hibernate.orm.deprecation            : HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-31 22:13:50.381  INFO 9428 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-31 22:13:50.383  INFO 9428 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-31 22:13:50.865  WARN 9428 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-31 22:13:50.998  INFO 9428 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@7a33171d, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@1e68164f, org.springframework.security.web.context.SecurityContextHolderFilter@61aa0f05, org.springframework.security.web.header.HeaderWriterFilter@8ca3a1, org.springframework.web.filter.CorsFilter@755f690f, org.springframework.security.web.authentication.logout.LogoutFilter@74f209b5, com.psychology.platform.security.JwtAuthenticationFilter@3b42ea2f, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5b71c9f7, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@3e05f893, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@a82d0cc, org.springframework.security.web.session.SessionManagementFilter@20cb6492, org.springframework.security.web.access.ExceptionTranslationFilter@48be4bec, org.springframework.security.web.access.intercept.AuthorizationFilter@3049fd7d]
2025-08-31 22:13:51.136  INFO 9428 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-08-31 22:13:51.151  INFO 9428 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/api'
2025-08-31 22:13:51.153  INFO 9428 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : Started PsychologyPlatformApplication in 1.573 seconds (process running for 100.444)
2025-08-31 22:13:51.154  INFO 9428 --- [restartedMain] .ConditionEvaluationDeltaLoggingListener : Condition evaluation unchanged
2025-08-31 22:14:20.326  INFO 9428 --- [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 9 class path changes (0 additions, 0 deletions, 9 modifications)
2025-08-31 22:14:20.332  INFO 9428 --- [Thread-7] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-31 22:14:20.332  INFO 9428 --- [Thread-7] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Shutdown initiated...
2025-08-31 22:14:20.333  INFO 9428 --- [Thread-7] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Shutdown completed.
2025-08-31 22:14:20.443  INFO 9428 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : Starting PsychologyPlatformApplication using Java 21.0.8 with PID 9428 (C:\Users\<USER>\Desktop\a\backend\target\classes started by 21653 in C:\Users\<USER>\Desktop\a\backend)
2025-08-31 22:14:20.444  INFO 9428 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : No active profile set, falling back to 1 default profile: "default"
2025-08-31 22:14:20.700  INFO 9428 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-31 22:14:20.717  INFO 9428 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 17 ms. Found 4 JPA repository interfaces.
2025-08-31 22:14:20.974  INFO 9428 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-08-31 22:14:20.975  INFO 9428 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-31 22:14:20.975  INFO 9428 --- [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-08-31 22:14:20.992  INFO 9428 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring embedded WebApplicationContext
2025-08-31 22:14:20.992  INFO 9428 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 545 ms
2025-08-31 22:14:21.059  INFO 9428 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-31 22:14:21.063  INFO 9428 --- [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-31 22:14:21.070  INFO 9428 --- [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-31 22:14:21.071  INFO 9428 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Starting...
2025-08-31 22:14:21.078  INFO 9428 --- [restartedMain] com.zaxxer.hikari.pool.HikariPool        : PsychologyPlatformHikariCP - Added connection com.mysql.cj.jdbc.ConnectionImpl@5708f82f
2025-08-31 22:14:21.078  INFO 9428 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Start completed.
2025-08-31 22:14:21.079  WARN 9428 --- [restartedMain] org.hibernate.orm.deprecation            : HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-31 22:14:21.239  INFO 9428 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-31 22:14:21.239  INFO 9428 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-31 22:14:21.867  WARN 9428 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-31 22:14:22.057  INFO 9428 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@3647f1b4, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@97d9c19, org.springframework.security.web.context.SecurityContextHolderFilter@1177cef6, org.springframework.security.web.header.HeaderWriterFilter@15a116cb, org.springframework.web.filter.CorsFilter@36ca5c89, org.springframework.security.web.authentication.logout.LogoutFilter@1235a61b, com.psychology.platform.security.JwtAuthenticationFilter@4faa0169, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@732ca902, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6e26eee2, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1515e0e4, org.springframework.security.web.session.SessionManagementFilter@78b57b45, org.springframework.security.web.access.ExceptionTranslationFilter@dddfc96, org.springframework.security.web.access.intercept.AuthorizationFilter@5d4fc662]
2025-08-31 22:14:22.248  INFO 9428 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-08-31 22:14:22.269  INFO 9428 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/api'
2025-08-31 22:14:22.273  INFO 9428 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : Started PsychologyPlatformApplication in 1.865 seconds (process running for 131.563)
2025-08-31 22:14:22.274  INFO 9428 --- [restartedMain] .ConditionEvaluationDeltaLoggingListener : Condition evaluation unchanged
2025-08-31 22:15:15.583  INFO 9428 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-31 22:15:15.584  INFO 9428 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Shutdown initiated...
2025-08-31 22:15:15.587  INFO 9428 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Shutdown completed.
2025-08-31 22:15:20.662  INFO 16400 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : Starting PsychologyPlatformApplication using Java 21.0.8 with PID 16400 (C:\Users\<USER>\Desktop\a\backend\target\classes started by 21653 in C:\Users\<USER>\Desktop\a\backend)
2025-08-31 22:15:20.668  INFO 16400 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : No active profile set, falling back to 1 default profile: "default"
2025-08-31 22:15:20.732  INFO 16400 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-31 22:15:20.732  INFO 16400 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-31 22:15:21.498  INFO 16400 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-31 22:15:21.558  INFO 16400 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 50 ms. Found 4 JPA repository interfaces.
2025-08-31 22:15:22.530  INFO 16400 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-08-31 22:15:22.544  INFO 16400 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-31 22:15:22.544  INFO 16400 --- [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-08-31 22:15:22.674  INFO 16400 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring embedded WebApplicationContext
2025-08-31 22:15:22.675  INFO 16400 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1941 ms
2025-08-31 22:15:22.930  INFO 16400 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-31 22:15:22.995  INFO 16400 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.3.1.Final
2025-08-31 22:15:23.055  INFO 16400 --- [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-31 22:15:23.367  INFO 16400 --- [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-31 22:15:23.394  INFO 16400 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Starting...
2025-08-31 22:15:23.634  INFO 16400 --- [restartedMain] com.zaxxer.hikari.pool.HikariPool        : PsychologyPlatformHikariCP - Added connection com.mysql.cj.jdbc.ConnectionImpl@70a687aa
2025-08-31 22:15:23.636  INFO 16400 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Start completed.
2025-08-31 22:15:23.674  WARN 16400 --- [restartedMain] org.hibernate.orm.deprecation            : HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-31 22:15:24.892  INFO 16400 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-31 22:15:24.896  INFO 16400 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-31 22:15:25.310  INFO 16400 --- [restartedMain] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-08-31 22:15:26.485  WARN 16400 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-31 22:15:27.107  INFO 16400 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@62b0176b, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@1fd4f106, org.springframework.security.web.context.SecurityContextHolderFilter@34b9ad58, org.springframework.security.web.header.HeaderWriterFilter@66c4c3c0, org.springframework.web.filter.CorsFilter@79634257, org.springframework.security.web.authentication.logout.LogoutFilter@9916461, com.psychology.platform.security.JwtAuthenticationFilter@7d1c18d0, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@35a4e711, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@25511752, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6a8e0493, org.springframework.security.web.session.SessionManagementFilter@445fef67, org.springframework.security.web.access.ExceptionTranslationFilter@7028999e, org.springframework.security.web.access.intercept.AuthorizationFilter@6083a358]
2025-08-31 22:15:27.703  INFO 16400 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-08-31 22:15:27.755  INFO 16400 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/api'
2025-08-31 22:15:27.765  INFO 16400 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : Started PsychologyPlatformApplication in 7.753 seconds (process running for 8.662)
2025-08-31 22:15:35.678  INFO 16400 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-31 22:15:35.679  INFO 16400 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-31 22:15:35.679  INFO 16400 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 0 ms
2025-08-31 22:15:35.719  WARN 16400 --- [http-nio-8080-exec-1] o.s.w.s.h.HandlerMappingIntrospector     : Cache miss for REQUEST dispatch to '/api/users/login' (previous null). Performing MatchableHandlerMapping lookup. This is logged once only at WARN level, and every time at TRACE.
2025-08-31 22:15:36.163  WARN 16400 --- [http-nio-8080-exec-1] .m.m.a.ExceptionHandlerExceptionResolver : Resolved [org.springframework.security.authentication.DisabledException: User is disabled]
2025-08-31 22:16:09.152  WARN 16400 --- [http-nio-8080-exec-9] .m.m.a.ExceptionHandlerExceptionResolver : Resolved [org.springframework.security.authentication.DisabledException: User is disabled]
2025-08-31 22:16:23.075  WARN 16400 --- [http-nio-8080-exec-10] .m.m.a.ExceptionHandlerExceptionResolver : Resolved [org.springframework.security.authentication.DisabledException: User is disabled]
2025-08-31 22:17:14.187  WARN 16400 --- [http-nio-8080-exec-1] .m.m.a.ExceptionHandlerExceptionResolver : Resolved [org.springframework.security.authentication.DisabledException: User is disabled]
2025-08-31 22:18:12.088  INFO 16400 --- [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-08-31 22:18:12.098  INFO 16400 --- [Thread-5] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-31 22:18:12.102  INFO 16400 --- [Thread-5] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Shutdown initiated...
2025-08-31 22:18:12.111  INFO 16400 --- [Thread-5] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Shutdown completed.
2025-08-31 22:18:12.234  INFO 16400 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : Starting PsychologyPlatformApplication using Java 21.0.8 with PID 16400 (C:\Users\<USER>\Desktop\a\backend\target\classes started by 21653 in C:\Users\<USER>\Desktop\a\backend)
2025-08-31 22:18:12.234  INFO 16400 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : No active profile set, falling back to 1 default profile: "default"
2025-08-31 22:18:12.497  INFO 16400 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-31 22:18:12.511  INFO 16400 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 13 ms. Found 4 JPA repository interfaces.
2025-08-31 22:18:12.698  INFO 16400 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-08-31 22:18:12.698  INFO 16400 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-31 22:18:12.698  INFO 16400 --- [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-08-31 22:18:12.719  INFO 16400 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring embedded WebApplicationContext
2025-08-31 22:18:12.719  INFO 16400 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 481 ms
2025-08-31 22:18:12.787  INFO 16400 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-31 22:18:12.791  INFO 16400 --- [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-31 22:18:12.796  INFO 16400 --- [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-31 22:18:12.796  INFO 16400 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Starting...
2025-08-31 22:18:12.803  INFO 16400 --- [restartedMain] com.zaxxer.hikari.pool.HikariPool        : PsychologyPlatformHikariCP - Added connection com.mysql.cj.jdbc.ConnectionImpl@f3df064
2025-08-31 22:18:12.803  INFO 16400 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Start completed.
2025-08-31 22:18:12.804  WARN 16400 --- [restartedMain] org.hibernate.orm.deprecation            : HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-31 22:18:12.944  INFO 16400 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-31 22:18:12.956  INFO 16400 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-31 22:18:13.379  WARN 16400 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-31 22:18:13.506  INFO 16400 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@1f9d2feb, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@392385f5, org.springframework.security.web.context.SecurityContextHolderFilter@19e245c6, org.springframework.security.web.header.HeaderWriterFilter@28b42b60, org.springframework.web.filter.CorsFilter@17d9b865, org.springframework.security.web.authentication.logout.LogoutFilter@3012c14c, com.psychology.platform.security.JwtAuthenticationFilter@1969b1e7, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@4d8c52f6, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@44ea9f0c, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@3e56246b, org.springframework.security.web.session.SessionManagementFilter@7581482c, org.springframework.security.web.access.ExceptionTranslationFilter@2dcf2eb2, org.springframework.security.web.access.intercept.AuthorizationFilter@fa4d04d]
2025-08-31 22:18:13.618  INFO 16400 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-08-31 22:18:13.631  INFO 16400 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/api'
2025-08-31 22:18:13.633  INFO 16400 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : Started PsychologyPlatformApplication in 1.449 seconds (process running for 174.53)
2025-08-31 22:18:13.634  INFO 16400 --- [restartedMain] .ConditionEvaluationDeltaLoggingListener : Condition evaluation unchanged
2025-08-31 22:19:08.555  INFO 16400 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-31 22:19:08.556  INFO 16400 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Shutdown initiated...
2025-08-31 22:19:08.558  INFO 16400 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Shutdown completed.
2025-08-31 22:19:12.769  INFO 17344 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : Starting PsychologyPlatformApplication using Java 21.0.8 with PID 17344 (C:\Users\<USER>\Desktop\a\backend\target\classes started by 21653 in C:\Users\<USER>\Desktop\a\backend)
2025-08-31 22:19:12.770  INFO 17344 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : No active profile set, falling back to 1 default profile: "default"
2025-08-31 22:19:12.819  INFO 17344 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-31 22:19:12.819  INFO 17344 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-31 22:19:13.393  INFO 17344 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-31 22:19:13.436  INFO 17344 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 35 ms. Found 4 JPA repository interfaces.
2025-08-31 22:19:14.192  INFO 17344 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-08-31 22:19:14.199  INFO 17344 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-31 22:19:14.200  INFO 17344 --- [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-08-31 22:19:14.243  INFO 17344 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring embedded WebApplicationContext
2025-08-31 22:19:14.243  INFO 17344 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1424 ms
2025-08-31 22:19:14.367  INFO 17344 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-31 22:19:14.428  INFO 17344 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.3.1.Final
2025-08-31 22:19:14.473  INFO 17344 --- [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-31 22:19:14.686  INFO 17344 --- [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-31 22:19:14.708  INFO 17344 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Starting...
2025-08-31 22:19:14.857  INFO 17344 --- [restartedMain] com.zaxxer.hikari.pool.HikariPool        : PsychologyPlatformHikariCP - Added connection com.mysql.cj.jdbc.ConnectionImpl@21ba1e91
2025-08-31 22:19:14.860  INFO 17344 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Start completed.
2025-08-31 22:19:14.897  WARN 17344 --- [restartedMain] org.hibernate.orm.deprecation            : HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-31 22:19:15.762  INFO 17344 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-31 22:19:15.765  INFO 17344 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-31 22:19:16.046  INFO 17344 --- [restartedMain] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-08-31 22:19:17.094  WARN 17344 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-31 22:19:17.460  INFO 17344 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@2866b621, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@23df0a3f, org.springframework.security.web.context.SecurityContextHolderFilter@f22b118, org.springframework.security.web.header.HeaderWriterFilter@106332cd, org.springframework.web.filter.CorsFilter@764402ed, org.springframework.security.web.authentication.logout.LogoutFilter@7ce0797b, com.psychology.platform.security.JwtAuthenticationFilter@2e6b40a0, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5136f61d, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@376d74be, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@356e8e93, org.springframework.security.web.session.SessionManagementFilter@3e3efe04, org.springframework.security.web.access.ExceptionTranslationFilter@4232e003, org.springframework.security.web.access.intercept.AuthorizationFilter@592a5309]
2025-08-31 22:19:17.879  INFO 17344 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-08-31 22:19:17.915  INFO 17344 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/api'
2025-08-31 22:19:17.921  INFO 17344 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : Started PsychologyPlatformApplication in 5.832 seconds (process running for 6.606)
2025-08-31 22:19:24.707  INFO 17344 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-31 22:19:24.708  INFO 17344 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-31 22:19:24.708  INFO 17344 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 0 ms
2025-08-31 22:19:24.732  WARN 17344 --- [http-nio-8080-exec-1] o.s.w.s.h.HandlerMappingIntrospector     : Cache miss for REQUEST dispatch to '/api/users/check/username' (previous null). Performing MatchableHandlerMapping lookup. This is logged once only at WARN level, and every time at TRACE.
2025-08-31 22:19:55.588  WARN 17344 --- [http-nio-8080-exec-7] .m.m.a.ExceptionHandlerExceptionResolver : Resolved [io.jsonwebtoken.security.WeakKeyException: The signing key's size is 312 bits which is not secure enough for the HS512 algorithm.  The JWT JWA Specification (RFC 7518, Section 3.2) states that keys used with HS512 MUST have a size >= 512 bits (the key size must be greater than or equal to the hash output size).  Consider using the io.jsonwebtoken.security.Keys class's 'secretKeyFor(SignatureAlgorithm.HS512)' method to create a key guaranteed to be secure enough for HS512.  See https://tools.ietf.org/html/rfc7518#section-3.2 for more information.]
2025-08-31 22:19:59.756  WARN 17344 --- [http-nio-8080-exec-8] .m.m.a.ExceptionHandlerExceptionResolver : Resolved [io.jsonwebtoken.security.WeakKeyException: The signing key's size is 312 bits which is not secure enough for the HS512 algorithm.  The JWT JWA Specification (RFC 7518, Section 3.2) states that keys used with HS512 MUST have a size >= 512 bits (the key size must be greater than or equal to the hash output size).  Consider using the io.jsonwebtoken.security.Keys class's 'secretKeyFor(SignatureAlgorithm.HS512)' method to create a key guaranteed to be secure enough for HS512.  See https://tools.ietf.org/html/rfc7518#section-3.2 for more information.]
2025-08-31 22:20:11.217  WARN 17344 --- [http-nio-8080-exec-5] .m.m.a.ExceptionHandlerExceptionResolver : Resolved [io.jsonwebtoken.security.WeakKeyException: The signing key's size is 312 bits which is not secure enough for the HS512 algorithm.  The JWT JWA Specification (RFC 7518, Section 3.2) states that keys used with HS512 MUST have a size >= 512 bits (the key size must be greater than or equal to the hash output size).  Consider using the io.jsonwebtoken.security.Keys class's 'secretKeyFor(SignatureAlgorithm.HS512)' method to create a key guaranteed to be secure enough for HS512.  See https://tools.ietf.org/html/rfc7518#section-3.2 for more information.]
2025-08-31 22:20:36.979  INFO 17344 --- [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 1 class path change (0 additions, 0 deletions, 1 modification)
2025-08-31 22:20:36.991  INFO 17344 --- [Thread-5] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-31 22:20:36.993  INFO 17344 --- [Thread-5] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Shutdown initiated...
2025-08-31 22:20:36.999  INFO 17344 --- [Thread-5] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Shutdown completed.
2025-08-31 22:20:37.095  INFO 17344 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : Starting PsychologyPlatformApplication using Java 21.0.8 with PID 17344 (C:\Users\<USER>\Desktop\a\backend\target\classes started by 21653 in C:\Users\<USER>\Desktop\a\backend)
2025-08-31 22:20:37.095  INFO 17344 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : No active profile set, falling back to 1 default profile: "default"
2025-08-31 22:20:37.243  INFO 17344 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-31 22:20:37.253  INFO 17344 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 4 JPA repository interfaces.
2025-08-31 22:20:37.489  INFO 17344 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-08-31 22:20:37.489  INFO 17344 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-31 22:20:37.489  INFO 17344 --- [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-08-31 22:20:37.516  INFO 17344 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring embedded WebApplicationContext
2025-08-31 22:20:37.516  INFO 17344 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 420 ms
2025-08-31 22:20:37.565  INFO 17344 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-31 22:20:37.568  INFO 17344 --- [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-31 22:20:37.573  INFO 17344 --- [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-31 22:20:37.574  INFO 17344 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Starting...
2025-08-31 22:20:37.579  INFO 17344 --- [restartedMain] com.zaxxer.hikari.pool.HikariPool        : PsychologyPlatformHikariCP - Added connection com.mysql.cj.jdbc.ConnectionImpl@504be62
2025-08-31 22:20:37.579  INFO 17344 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Start completed.
2025-08-31 22:20:37.580  WARN 17344 --- [restartedMain] org.hibernate.orm.deprecation            : HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-31 22:20:37.695  INFO 17344 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-31 22:20:37.695  INFO 17344 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-31 22:20:38.128  WARN 17344 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-31 22:20:38.246  INFO 17344 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@39199cf2, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@20c7e85, org.springframework.security.web.context.SecurityContextHolderFilter@6dea7832, org.springframework.security.web.header.HeaderWriterFilter@5468e14c, org.springframework.web.filter.CorsFilter@45317bc9, org.springframework.security.web.authentication.logout.LogoutFilter@e36b31f, com.psychology.platform.security.JwtAuthenticationFilter@2a09b306, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@62d080a3, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@71216f34, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6c17c1cb, org.springframework.security.web.session.SessionManagementFilter@5aa36c15, org.springframework.security.web.access.ExceptionTranslationFilter@1a32a0ec, org.springframework.security.web.access.intercept.AuthorizationFilter@12a77519]
2025-08-31 22:20:38.360  INFO 17344 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-08-31 22:20:38.377  INFO 17344 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/api'
2025-08-31 22:20:38.381  INFO 17344 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : Started PsychologyPlatformApplication in 1.318 seconds (process running for 87.065)
2025-08-31 22:20:38.383  INFO 17344 --- [restartedMain] .ConditionEvaluationDeltaLoggingListener : Condition evaluation unchanged
2025-08-31 22:21:02.071  INFO 17344 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-31 22:21:02.072  INFO 17344 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Shutdown initiated...
2025-08-31 22:21:02.073  INFO 17344 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Shutdown completed.
2025-08-31 22:21:05.136  INFO 20232 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : Starting PsychologyPlatformApplication using Java 21.0.8 with PID 20232 (C:\Users\<USER>\Desktop\a\backend\target\classes started by 21653 in C:\Users\<USER>\Desktop\a\backend)
2025-08-31 22:21:05.138  INFO 20232 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : No active profile set, falling back to 1 default profile: "default"
2025-08-31 22:21:05.196  INFO 20232 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-31 22:21:05.196  INFO 20232 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-31 22:21:05.891  INFO 20232 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-31 22:21:05.950  INFO 20232 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 50 ms. Found 4 JPA repository interfaces.
2025-08-31 22:21:06.786  INFO 20232 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-08-31 22:21:06.795  INFO 20232 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-31 22:21:06.795  INFO 20232 --- [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-08-31 22:21:06.846  INFO 20232 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring embedded WebApplicationContext
2025-08-31 22:21:06.846  INFO 20232 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1649 ms
2025-08-31 22:21:06.987  INFO 20232 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-31 22:21:07.055  INFO 20232 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.3.1.Final
2025-08-31 22:21:07.093  INFO 20232 --- [restartedMain] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-31 22:21:07.314  INFO 20232 --- [restartedMain] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-31 22:21:07.348  INFO 20232 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Starting...
2025-08-31 22:21:07.662  INFO 20232 --- [restartedMain] com.zaxxer.hikari.pool.HikariPool        : PsychologyPlatformHikariCP - Added connection com.mysql.cj.jdbc.ConnectionImpl@7802d45f
2025-08-31 22:21:07.664  INFO 20232 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : PsychologyPlatformHikariCP - Start completed.
2025-08-31 22:21:07.711  WARN 20232 --- [restartedMain] org.hibernate.orm.deprecation            : HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-31 22:21:10.156  INFO 20232 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-31 22:21:10.162  INFO 20232 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-31 22:21:10.721  INFO 20232 --- [restartedMain] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-08-31 22:21:12.651  WARN 20232 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-31 22:21:13.161  INFO 20232 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@1d719717, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@36c63189, org.springframework.security.web.context.SecurityContextHolderFilter@6db27d6e, org.springframework.security.web.header.HeaderWriterFilter@54422383, org.springframework.web.filter.CorsFilter@411d0aef, org.springframework.security.web.authentication.logout.LogoutFilter@1e5c76e5, com.psychology.platform.security.JwtAuthenticationFilter@5792235c, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@7373033a, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@3aac2865, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7de0b67, org.springframework.security.web.session.SessionManagementFilter@4239c80f, org.springframework.security.web.access.ExceptionTranslationFilter@6f45c02, org.springframework.security.web.access.intercept.AuthorizationFilter@2c90f189]
2025-08-31 22:21:13.703  INFO 20232 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-08-31 22:21:13.824  INFO 20232 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path '/api'
2025-08-31 22:21:13.848  INFO 20232 --- [restartedMain] c.p.p.PsychologyPlatformApplication      : Started PsychologyPlatformApplication in 9.335 seconds (process running for 10.385)
2025-08-31 22:21:21.722  INFO 20232 --- [http-nio-8080-exec-5] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-31 22:21:21.722  INFO 20232 --- [http-nio-8080-exec-5] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-31 22:21:21.723  INFO 20232 --- [http-nio-8080-exec-5] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-08-31 22:21:21.749  WARN 20232 --- [http-nio-8080-exec-5] o.s.w.s.h.HandlerMappingIntrospector     : Cache miss for REQUEST dispatch to '/api/users/login' (previous null). Performing MatchableHandlerMapping lookup. This is logged once only at WARN level, and every time at TRACE.
2025-08-31 22:21:22.233  WARN 20232 --- [http-nio-8080-exec-5] .m.m.a.ExceptionHandlerExceptionResolver : Resolved [java.lang.RuntimeException: 用户不存在]
2025-08-31 22:22:02.104  WARN 20232 --- [http-nio-8080-exec-4] .m.m.a.ExceptionHandlerExceptionResolver : Resolved [java.lang.RuntimeException: 用户不存在]
2025-08-31 22:22:35.466  WARN 20232 --- [http-nio-8080-exec-7] .m.m.a.ExceptionHandlerExceptionResolver : Resolved [java.lang.RuntimeException: 用户不存在]
