package com.psychology.platform.repository;

import com.psychology.platform.entity.CounselingSession;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 咨询会话数据访问层
 * 提供咨询会话相关的数据库操作方法
 * 
 * <AUTHOR> Platform Team
 * @version 1.0.0
 */
@Repository
public interface CounselingSessionRepository extends JpaRepository<CounselingSession, Long> {

    /**
     * 根据预约ID查找咨询会话
     */
    Optional<CounselingSession> findByAppointmentId(Long appointmentId);

    /**
     * 根据状态查找咨询会话
     */
    Page<CounselingSession> findByStatus(CounselingSession.SessionStatus status, Pageable pageable);

    /**
     * 根据用户ID查找咨询会话
     */
    @Query("SELECT cs FROM CounselingSession cs JOIN cs.appointment a WHERE a.user.id = :userId")
    Page<CounselingSession> findByUserId(@Param("userId") Long userId, Pageable pageable);

    /**
     * 根据咨询师ID查找咨询会话
     */
    @Query("SELECT cs FROM CounselingSession cs JOIN cs.appointment a WHERE a.counselor.id = :counselorId")
    Page<CounselingSession> findByCounselorId(@Param("counselorId") Long counselorId, Pageable pageable);

    /**
     * 查找指定时间范围内的咨询会话
     */
    @Query("SELECT cs FROM CounselingSession cs WHERE cs.startTime BETWEEN :startTime AND :endTime")
    Page<CounselingSession> findByStartTimeBetween(@Param("startTime") LocalDateTime startTime, 
                                                  @Param("endTime") LocalDateTime endTime, 
                                                  Pageable pageable);

    /**
     * 查找需要跟进的咨询会话
     */
    @Query("SELECT cs FROM CounselingSession cs WHERE cs.followUpRequired = true AND cs.followUpDate <= :currentTime")
    List<CounselingSession> findSessionsNeedingFollowUp(@Param("currentTime") LocalDateTime currentTime);

    /**
     * 查找紧急情况的咨询会话
     */
    Page<CounselingSession> findByIsEmergency(Boolean isEmergency, Pageable pageable);

    /**
     * 查找有用户评分的咨询会话
     */
    @Query("SELECT cs FROM CounselingSession cs WHERE cs.userRating IS NOT NULL")
    Page<CounselingSession> findSessionsWithRating(Pageable pageable);

    /**
     * 根据用户评分范围查找咨询会话
     */
    @Query("SELECT cs FROM CounselingSession cs WHERE cs.userRating BETWEEN :minRating AND :maxRating")
    Page<CounselingSession> findByUserRatingBetween(@Param("minRating") Integer minRating, 
                                                   @Param("maxRating") Integer maxRating, 
                                                   Pageable pageable);

    /**
     * 统计咨询师的会话总数
     */
    @Query("SELECT COUNT(cs) FROM CounselingSession cs JOIN cs.appointment a WHERE a.counselor.id = :counselorId AND cs.status = 'COMPLETED'")
    long countCompletedSessionsByCounselorId(@Param("counselorId") Long counselorId);

    /**
     * 统计用户的会话总数
     */
    @Query("SELECT COUNT(cs) FROM CounselingSession cs JOIN cs.appointment a WHERE a.user.id = :userId AND cs.status = 'COMPLETED'")
    long countCompletedSessionsByUserId(@Param("userId") Long userId);

    /**
     * 计算咨询师的平均评分
     */
    @Query("SELECT AVG(cs.userRating) FROM CounselingSession cs JOIN cs.appointment a WHERE a.counselor.id = :counselorId AND cs.userRating IS NOT NULL")
    Double calculateAverageRatingByCounselorId(@Param("counselorId") Long counselorId);

    /**
     * 查找今日进行中的咨询会话
     */
    @Query("SELECT cs FROM CounselingSession cs WHERE cs.status = 'IN_PROGRESS' AND FUNCTION('DATE', cs.startTime) = FUNCTION('DATE', :today)")
    List<CounselingSession> findTodayInProgressSessions(@Param("today") LocalDateTime today);

    /**
     * 查找已完成但未评分的咨询会话
     */
    @Query("SELECT cs FROM CounselingSession cs WHERE cs.status = 'COMPLETED' AND cs.userRating IS NULL")
    Page<CounselingSession> findCompletedSessionsWithoutRating(Pageable pageable);

    /**
     * 统计各状态的会话数量
     */
    long countByStatus(CounselingSession.SessionStatus status);

    /**
     * 查找有录音的咨询会话
     */
    @Query("SELECT cs FROM CounselingSession cs WHERE cs.recordingUrl IS NOT NULL AND cs.recordingUrl != ''")
    Page<CounselingSession> findSessionsWithRecording(Pageable pageable);
}
