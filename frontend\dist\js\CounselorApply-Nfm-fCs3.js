import{c as i,a as s,h as c,U as V,y as n,n as f,i as r,Z as m,_ as y,b as R,A as S,$ as w,d as F,w as j,e as z,r as g,z as k,o as L,E as u,m as a}from"./index-D4DFMkO1.js";import{_ as M}from"./_plugin-vue_export-helper-DlAUqK2U.js";const U={name:"CounselorApply",setup(){const v=g(!1),o=g(""),D=g(""),e=k({licenseNumber:"",specialization:"",yearsOfExperience:"",hourlyRate:"",education:"",university:"",major:"",professionalSummary:"",workExperience:"",achievements:"",licenseFile:null,diplomaFile:null,agreeToTerms:!1}),b=k({name:"",specialization:"",rating:0,reviewCount:0,hourlyRate:0,yearsOfExperience:0,totalSessions:0,avatarUrl:""}),T=l=>({PENDING:"status-pending",APPROVED:"status-approved",REJECTED:"status-rejected"})[l]||"",E=l=>({PENDING:"申请审核中",APPROVED:"申请已通过",REJECTED:"申请被拒绝"})[l]||"",t=l=>({PENDING:"您的咨询师申请正在审核中，我们将在3-5个工作日内完成审核并通知您结果。",APPROVED:"恭喜！您的咨询师申请已通过审核，现在可以开始提供咨询服务了。",REJECTED:"很抱歉，您的咨询师申请未通过审核。您可以根据拒绝原因修改后重新申请。"})[l]||"",x=l=>({ANXIETY_DEPRESSION:"焦虑抑郁",RELATIONSHIP:"情感关系",FAMILY_THERAPY:"家庭治疗",CHILD_ADOLESCENT:"儿童青少年",CAREER_DEVELOPMENT:"职业发展",TRAUMA_PTSD:"创伤与PTSD",ADDICTION:"成瘾治疗",OTHER:"其他"})[l]||l,P=()=>{const l=document.createElement("input");l.type="file",l.accept=".jpg,.jpeg,.png,.pdf",l.onchange=d=>{const p=d.target.files[0];p&&p.size<=5*1024*1024?e.licenseFile=p:u.error("文件大小不能超过5MB")},l.click()},N=()=>{e.licenseFile=null},C=()=>{const l=document.createElement("input");l.type="file",l.accept=".jpg,.jpeg,.png,.pdf",l.onchange=d=>{const p=d.target.files[0];p&&p.size<=5*1024*1024?e.diplomaFile=p:u.error("文件大小不能超过5MB")},l.click()},I=()=>{e.diplomaFile=null},O=async()=>{if(!e.licenseFile){u.error("请上传执业证书");return}if(e.professionalSummary.length<50){u.error("个人简介至少需要50字");return}v.value=!0;try{await new Promise(l=>setTimeout(l,2e3)),o.value="PENDING",u.success("申请提交成功，请等待审核结果")}catch{u.error("申请提交失败，请重试")}finally{v.value=!1}},A=()=>{Object.assign(e,{licenseNumber:"",specialization:"",yearsOfExperience:"",hourlyRate:"",education:"",university:"",major:"",professionalSummary:"",workExperience:"",achievements:"",licenseFile:null,diplomaFile:null,agreeToTerms:!1})},_=async()=>{try{await new Promise(l=>setTimeout(l,1e3)),o.value==="APPROVED"&&Object.assign(b,{name:"张心理师",specialization:"ANXIETY_DEPRESSION",rating:4.8,reviewCount:156,hourlyRate:200,yearsOfExperience:5,totalSessions:320,avatarUrl:""})}catch(l){console.error("检查申请状态失败:",l)}};return L(()=>{_()}),{loading:v,applicationStatus:o,rejectionReason:D,form:e,counselorInfo:b,getStatusClass:T,getStatusTitle:E,getStatusDescription:t,getSpecializationText:x,uploadLicense:P,removeLicense:N,uploadDiploma:C,removeDiploma:I,handleSubmit:O,resetForm:A}}},h={class:"counselor-apply-container"},q={class:"status-icon"},G={key:0},H={key:1},J={key:2},B={class:"status-content"},Y={key:0,class:"rejection-reason"},X={key:1,class:"apply-form-section"},Z={class:"form-header"},K={class:"form-section"},Q={class:"form-group"},W={class:"form-group"},$={class:"form-group"},oo={class:"form-group"},so={class:"form-section"},eo={class:"form-group"},to={class:"form-group"},lo={class:"form-group"},io={class:"form-section"},no={class:"form-group"},ao={class:"form-hint"},ro={class:"form-group"},po={class:"form-group"},mo={class:"form-section"},uo={class:"form-group"},co={key:0,class:"upload-placeholder"},fo={key:1,class:"upload-preview"},vo={class:"file-info"},bo={class:"file-name"},Eo={class:"form-group"},yo={key:0,class:"upload-placeholder"},So={key:1,class:"upload-preview"},go={class:"file-info"},Do={class:"file-name"},To={class:"form-section"},Ro={class:"agreement-section"},ko={class:"checkbox-label"},xo={class:"form-actions"},Po=["disabled"],No={key:0},Co={key:1},Io={key:2,class:"counselor-info-section"},Oo={class:"counselor-card"},Ao={class:"counselor-header"},_o=["src"],Vo={class:"counselor-details"},wo={class:"specialization"},Fo={class:"rating"},jo={class:"counselor-stats"},zo={class:"stat-item"},Lo={class:"value"},Mo={class:"stat-item"},Uo={class:"value"},ho={class:"stat-item"},qo={class:"value"},Go={class:"counselor-actions"};function Ho(v,o,D,e,b,T){const E=z("router-link");return a(),i("div",h,[o[53]||(o[53]=s("div",{class:"apply-header"},[s("h2",null,"申请成为咨询师"),s("p",{class:"subtitle"},"加入我们的专业咨询师团队，帮助更多需要心理支持的人")],-1)),e.applicationStatus?(a(),i("div",{key:0,class:V(["status-card",e.getStatusClass(e.applicationStatus)])},[s("div",q,[e.applicationStatus==="PENDING"?(a(),i("span",G,"⏳")):e.applicationStatus==="APPROVED"?(a(),i("span",H,"✅")):e.applicationStatus==="REJECTED"?(a(),i("span",J,"❌")):c("",!0)]),s("div",B,[s("h3",null,n(e.getStatusTitle(e.applicationStatus)),1),s("p",null,n(e.getStatusDescription(e.applicationStatus)),1),e.applicationStatus==="REJECTED"&&e.rejectionReason?(a(),i("div",Y,[o[17]||(o[17]=s("strong",null,"拒绝原因：",-1)),f(n(e.rejectionReason),1)])):c("",!0)])],2)):c("",!0),!e.applicationStatus||e.applicationStatus==="REJECTED"?(a(),i("div",X,[s("div",Z,[s("h3",null,n(e.applicationStatus==="REJECTED"?"重新申请":"填写申请信息"),1),o[18]||(o[18]=s("p",null,"请如实填写以下信息，我们将在3-5个工作日内完成审核",-1))]),s("form",{onSubmit:o[16]||(o[16]=S((...t)=>e.handleSubmit&&e.handleSubmit(...t),["prevent"])),class:"apply-form"},[s("div",K,[o[27]||(o[27]=s("h4",null,"基本信息",-1)),s("div",Q,[o[19]||(o[19]=s("label",{for:"licenseNumber"},"执业证书编号 *",-1)),r(s("input",{id:"licenseNumber","onUpdate:modelValue":o[0]||(o[0]=t=>e.form.licenseNumber=t),type:"text",class:"form-input",placeholder:"请输入心理咨询师执业证书编号",required:""},null,512),[[m,e.form.licenseNumber]]),o[20]||(o[20]=s("small",{class:"form-hint"},"请确保证书编号真实有效，我们将进行核实",-1))]),s("div",W,[o[22]||(o[22]=s("label",{for:"specialization"},"专业领域 *",-1)),r(s("select",{id:"specialization","onUpdate:modelValue":o[1]||(o[1]=t=>e.form.specialization=t),class:"form-select",required:""},[...o[21]||(o[21]=[R('<option value="" data-v-b2bb1315>请选择专业领域</option><option value="ANXIETY_DEPRESSION" data-v-b2bb1315>焦虑抑郁</option><option value="RELATIONSHIP" data-v-b2bb1315>情感关系</option><option value="FAMILY_THERAPY" data-v-b2bb1315>家庭治疗</option><option value="CHILD_ADOLESCENT" data-v-b2bb1315>儿童青少年</option><option value="CAREER_DEVELOPMENT" data-v-b2bb1315>职业发展</option><option value="TRAUMA_PTSD" data-v-b2bb1315>创伤与PTSD</option><option value="ADDICTION" data-v-b2bb1315>成瘾治疗</option><option value="OTHER" data-v-b2bb1315>其他</option>',9)])],512),[[y,e.form.specialization]])]),s("div",$,[o[24]||(o[24]=s("label",{for:"yearsOfExperience"},"从业年限 *",-1)),r(s("select",{id:"yearsOfExperience","onUpdate:modelValue":o[2]||(o[2]=t=>e.form.yearsOfExperience=t),class:"form-select",required:""},[...o[23]||(o[23]=[R('<option value="" data-v-b2bb1315>请选择从业年限</option><option value="1" data-v-b2bb1315>1年以下</option><option value="2" data-v-b2bb1315>1-3年</option><option value="5" data-v-b2bb1315>3-5年</option><option value="10" data-v-b2bb1315>5-10年</option><option value="15" data-v-b2bb1315>10年以上</option>',6)])],512),[[y,e.form.yearsOfExperience]])]),s("div",oo,[o[25]||(o[25]=s("label",{for:"hourlyRate"},"咨询费用（元/小时）*",-1)),r(s("input",{id:"hourlyRate","onUpdate:modelValue":o[3]||(o[3]=t=>e.form.hourlyRate=t),type:"number",class:"form-input",placeholder:"请输入您的咨询费用",min:"50",max:"2000",required:""},null,512),[[m,e.form.hourlyRate]]),o[26]||(o[26]=s("small",{class:"form-hint"},"建议费用范围：50-2000元/小时",-1))])]),s("div",so,[o[32]||(o[32]=s("h4",null,"教育背景",-1)),s("div",eo,[o[29]||(o[29]=s("label",{for:"education"},"最高学历 *",-1)),r(s("select",{id:"education","onUpdate:modelValue":o[4]||(o[4]=t=>e.form.education=t),class:"form-select",required:""},[...o[28]||(o[28]=[s("option",{value:""},"请选择学历",-1),s("option",{value:"BACHELOR"},"本科",-1),s("option",{value:"MASTER"},"硕士",-1),s("option",{value:"DOCTOR"},"博士",-1)])],512),[[y,e.form.education]])]),s("div",to,[o[30]||(o[30]=s("label",{for:"university"},"毕业院校 *",-1)),r(s("input",{id:"university","onUpdate:modelValue":o[5]||(o[5]=t=>e.form.university=t),type:"text",class:"form-input",placeholder:"请输入毕业院校名称",required:""},null,512),[[m,e.form.university]])]),s("div",lo,[o[31]||(o[31]=s("label",{for:"major"},"专业 *",-1)),r(s("input",{id:"major","onUpdate:modelValue":o[6]||(o[6]=t=>e.form.major=t),type:"text",class:"form-input",placeholder:"请输入所学专业",required:""},null,512),[[m,e.form.major]])])]),s("div",io,[o[36]||(o[36]=s("h4",null,"专业描述",-1)),s("div",no,[o[33]||(o[33]=s("label",{for:"professionalSummary"},"个人简介 *",-1)),r(s("textarea",{id:"professionalSummary","onUpdate:modelValue":o[7]||(o[7]=t=>e.form.professionalSummary=t),class:"form-textarea",placeholder:"请详细介绍您的专业背景、治疗理念、擅长领域等（建议200-500字）",rows:"6",required:""},null,512),[[m,e.form.professionalSummary]]),s("small",ao,n(e.form.professionalSummary.length)+"/500字",1)]),s("div",ro,[o[34]||(o[34]=s("label",{for:"workExperience"},"工作经历",-1)),r(s("textarea",{id:"workExperience","onUpdate:modelValue":o[8]||(o[8]=t=>e.form.workExperience=t),class:"form-textarea",placeholder:"请描述您的相关工作经历（可选）",rows:"4"},null,512),[[m,e.form.workExperience]])]),s("div",po,[o[35]||(o[35]=s("label",{for:"achievements"},"专业成就",-1)),r(s("textarea",{id:"achievements","onUpdate:modelValue":o[9]||(o[9]=t=>e.form.achievements=t),class:"form-textarea",placeholder:"请列举您的专业成就、获奖情况、发表论文等（可选）",rows:"4"},null,512),[[m,e.form.achievements]])])]),s("div",mo,[o[41]||(o[41]=s("h4",null,"证书上传",-1)),s("div",uo,[o[38]||(o[38]=s("label",null,"执业证书 *",-1)),s("div",{class:"upload-area",onClick:o[11]||(o[11]=(...t)=>e.uploadLicense&&e.uploadLicense(...t))},[e.form.licenseFile?(a(),i("div",fo,[s("div",vo,[s("span",bo,n(e.form.licenseFile.name),1),s("button",{type:"button",onClick:o[10]||(o[10]=S((...t)=>e.removeLicense&&e.removeLicense(...t),["stop"])),class:"remove-btn"},"×")])])):(a(),i("div",co,[...o[37]||(o[37]=[s("div",{class:"upload-icon"},"📄",-1),s("p",null,"点击上传执业证书",-1),s("small",null,"支持 JPG、PNG、PDF 格式，大小不超过 5MB",-1)])]))])]),s("div",Eo,[o[40]||(o[40]=s("label",null,"学历证书",-1)),s("div",{class:"upload-area",onClick:o[13]||(o[13]=(...t)=>e.uploadDiploma&&e.uploadDiploma(...t))},[e.form.diplomaFile?(a(),i("div",So,[s("div",go,[s("span",Do,n(e.form.diplomaFile.name),1),s("button",{type:"button",onClick:o[12]||(o[12]=S((...t)=>e.removeDiploma&&e.removeDiploma(...t),["stop"])),class:"remove-btn"},"×")])])):(a(),i("div",yo,[...o[39]||(o[39]=[s("div",{class:"upload-icon"},"🎓",-1),s("p",null,"点击上传学历证书（可选）",-1),s("small",null,"支持 JPG、PNG、PDF 格式，大小不超过 5MB",-1)])]))])])]),s("div",To,[s("div",Ro,[s("label",ko,[r(s("input",{type:"checkbox","onUpdate:modelValue":o[14]||(o[14]=t=>e.form.agreeToTerms=t),required:""},null,512),[[w,e.form.agreeToTerms]]),o[42]||(o[42]=s("span",{class:"checkmark"},null,-1)),o[43]||(o[43]=f(" 我已阅读并同意 ",-1)),o[44]||(o[44]=s("a",{href:"#",class:"link"},"《咨询师服务协议》",-1)),o[45]||(o[45]=f(" 和 ",-1)),o[46]||(o[46]=s("a",{href:"#",class:"link"},"《平台使用条款》",-1))])])]),s("div",xo,[s("button",{type:"submit",class:"btn btn-primary btn-large",disabled:e.loading},[e.loading?(a(),i("span",No,"提交中...")):(a(),i("span",Co,"提交申请"))],8,Po),s("button",{type:"button",class:"btn btn-secondary btn-large",onClick:o[15]||(o[15]=(...t)=>e.resetForm&&e.resetForm(...t))}," 重置表单 ")])],32)])):c("",!0),e.applicationStatus==="APPROVED"?(a(),i("div",Io,[o[52]||(o[52]=s("h3",null,"咨询师信息",-1)),s("div",Oo,[s("div",Ao,[s("img",{src:e.counselorInfo.avatarUrl||"/default-avatar.png",alt:"头像",class:"counselor-avatar"},null,8,_o),s("div",Vo,[s("h4",null,n(e.counselorInfo.name),1),s("p",wo,n(e.getSpecializationText(e.counselorInfo.specialization)),1),s("p",Fo,[o[47]||(o[47]=s("span",{class:"stars"},"⭐",-1)),f(" "+n(e.counselorInfo.rating)+" ("+n(e.counselorInfo.reviewCount)+"条评价) ",1)])])]),s("div",jo,[s("div",zo,[o[48]||(o[48]=s("span",{class:"label"},"咨询费用：",-1)),s("span",Lo,"¥"+n(e.counselorInfo.hourlyRate)+"/小时",1)]),s("div",Mo,[o[49]||(o[49]=s("span",{class:"label"},"从业年限：",-1)),s("span",Uo,n(e.counselorInfo.yearsOfExperience)+"年",1)]),s("div",ho,[o[50]||(o[50]=s("span",{class:"label"},"总咨询次数：",-1)),s("span",qo,n(e.counselorInfo.totalSessions)+"次",1)])]),s("div",Go,[F(E,{to:"/dashboard/counselor-center",class:"btn btn-primary"},{default:j(()=>[...o[51]||(o[51]=[f(" 进入咨询师中心 ",-1)])]),_:1})])])])):c("",!0)])}const Yo=M(U,[["render",Ho],["__scopeId","data-v-b2bb1315"]]);export{Yo as default};
