<template>
  <div class="booking-container">
    <div class="booking-header">
      <h2>预约咨询</h2>
      <p class="subtitle">选择合适的时间，开始您的心理咨询之旅</p>
    </div>

    <div v-if="loading" class="loading">
      <p>加载中...</p>
    </div>

    <div v-else-if="!counselor" class="error-state">
      <h3>咨询师信息加载失败</h3>
      <p>请返回重新选择咨询师</p>
      <router-link to="/counselors" class="btn btn-primary">
        返回咨询师列表
      </router-link>
    </div>

    <div v-else class="booking-content">
      <!-- 咨询师信息 -->
      <div class="counselor-card">
        <div class="counselor-header">
          <img 
            :src="counselor.avatarUrl || '/default-avatar.png'" 
            :alt="counselor.name"
            class="counselor-avatar"
          >
          <div class="counselor-info">
            <h3>{{ counselor.name }}</h3>
            <p class="specialization">{{ getSpecializationText(counselor.specialization) }}</p>
            <div class="rating">
              <span class="stars">⭐</span>
              {{ counselor.rating }} ({{ counselor.reviewCount }}条评价)
            </div>
            <div class="price">
              <span class="amount">¥{{ counselor.hourlyRate }}</span>
              <span class="unit">/小时</span>
            </div>
          </div>
        </div>
        
        <div class="counselor-summary">
          <p>{{ counselor.professionalSummary }}</p>
        </div>
      </div>

      <!-- 预约表单 -->
      <div class="booking-form">
        <form @submit.prevent="handleSubmit">
          <!-- 咨询方式选择 -->
          <div class="form-section">
            <h4>选择咨询方式</h4>
            <div class="consultation-types">
              <label 
                v-for="type in consultationTypes" 
                :key="type.value"
                class="type-option"
                :class="{ active: form.consultationType === type.value }"
              >
                <input 
                  type="radio" 
                  :value="type.value" 
                  v-model="form.consultationType"
                  @change="updatePrice"
                >
                <div class="type-content">
                  <div class="type-icon">{{ type.icon }}</div>
                  <div class="type-info">
                    <h5>{{ type.label }}</h5>
                    <p>{{ type.description }}</p>
                    <span class="type-price">¥{{ getTypePrice(type.value) }}/小时</span>
                  </div>
                </div>
              </label>
            </div>
          </div>

          <!-- 时间选择 -->
          <div class="form-section">
            <h4>选择咨询时间</h4>
            
            <!-- 日期选择 -->
            <div class="date-selection">
              <label for="appointmentDate">预约日期：</label>
              <input 
                id="appointmentDate"
                type="date" 
                v-model="form.appointmentDate"
                :min="minDate"
                :max="maxDate"
                @change="loadAvailableSlots"
                class="form-input"
                required
              >
            </div>

            <!-- 时间段选择 -->
            <div v-if="form.appointmentDate" class="time-slots">
              <h5>可预约时间段：</h5>
              <div v-if="loadingSlots" class="loading-slots">
                <p>加载时间段中...</p>
              </div>
              <div v-else-if="availableSlots.length === 0" class="no-slots">
                <p>该日期暂无可预约时间段</p>
              </div>
              <div v-else class="slot-grid">
                <label 
                  v-for="slot in availableSlots" 
                  :key="slot.time"
                  class="slot-option"
                  :class="{ active: form.appointmentTime === slot.time }"
                >
                  <input 
                    type="radio" 
                    :value="slot.time" 
                    v-model="form.appointmentTime"
                  >
                  <span class="slot-time">{{ slot.time }}</span>
                </label>
              </div>
            </div>
          </div>

          <!-- 咨询时长 -->
          <div class="form-section">
            <h4>咨询时长</h4>
            <div class="duration-options">
              <label 
                v-for="duration in durationOptions" 
                :key="duration.value"
                class="duration-option"
                :class="{ active: form.duration === duration.value }"
              >
                <input 
                  type="radio" 
                  :value="duration.value" 
                  v-model="form.duration"
                  @change="updatePrice"
                >
                <span class="duration-text">{{ duration.label }}</span>
              </label>
            </div>
          </div>

          <!-- 备注信息 -->
          <div class="form-section">
            <h4>备注信息</h4>
            <div class="form-group">
              <label for="notes">咨询需求描述（可选）：</label>
              <textarea 
                id="notes"
                v-model="form.notes" 
                class="form-textarea"
                placeholder="请简要描述您希望咨询的问题或需求，这将帮助咨询师更好地为您服务..."
                rows="4"
              ></textarea>
              <small class="form-hint">{{ form.notes.length }}/500字</small>
            </div>
          </div>

          <!-- 费用明细 -->
          <div class="cost-summary">
            <h4>费用明细</h4>
            <div class="cost-details">
              <div class="cost-item">
                <span class="label">咨询费用：</span>
                <span class="value">¥{{ getTypePrice(form.consultationType) }}/小时</span>
              </div>
              <div class="cost-item">
                <span class="label">咨询时长：</span>
                <span class="value">{{ form.duration }}分钟</span>
              </div>
              <div class="cost-item total">
                <span class="label">总费用：</span>
                <span class="value">¥{{ totalAmount }}</span>
              </div>
            </div>
          </div>

          <!-- 提交按钮 -->
          <div class="form-actions">
            <button type="submit" class="btn btn-primary btn-large" :disabled="submitting || !isFormValid">
              <span v-if="submitting">提交中...</span>
              <span v-else>确认预约</span>
            </button>
            <button type="button" class="btn btn-secondary btn-large" @click="goBack">
              返回
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'

export default {
  name: 'Booking',
  setup() {
    const route = useRoute()
    const router = useRouter()
    
    const loading = ref(false)
    const submitting = ref(false)
    const loadingSlots = ref(false)
    const counselor = ref(null)
    const availableSlots = ref([])

    // 预约表单
    const form = reactive({
      consultationType: 'VIDEO',
      appointmentDate: '',
      appointmentTime: '',
      duration: 60,
      notes: ''
    })

    // 咨询方式选项
    const consultationTypes = [
      {
        value: 'VIDEO',
        label: '视频咨询',
        description: '面对面视频交流，体验最佳',
        icon: '📹',
        priceMultiplier: 1.0
      },
      {
        value: 'VOICE',
        label: '语音咨询',
        description: '语音通话，保护隐私',
        icon: '🎙️',
        priceMultiplier: 0.9
      },
      {
        value: 'TEXT',
        label: '文字咨询',
        description: '文字交流，随时回顾',
        icon: '💬',
        priceMultiplier: 0.8
      }
    ]

    // 时长选项
    const durationOptions = [
      { value: 45, label: '45分钟' },
      { value: 60, label: '60分钟' },
      { value: 90, label: '90分钟' }
    ]

    // 计算属性
    const minDate = computed(() => {
      const tomorrow = new Date()
      tomorrow.setDate(tomorrow.getDate() + 1)
      return tomorrow.toISOString().split('T')[0]
    })

    const maxDate = computed(() => {
      const maxDate = new Date()
      maxDate.setDate(maxDate.getDate() + 30)
      return maxDate.toISOString().split('T')[0]
    })

    const totalAmount = computed(() => {
      if (!counselor.value) return 0
      const basePrice = getTypePrice(form.consultationType)
      return Math.round(basePrice * (form.duration / 60))
    })

    const isFormValid = computed(() => {
      return form.consultationType && 
             form.appointmentDate && 
             form.appointmentTime && 
             form.duration
    })

    // 获取专业领域文本
    const getSpecializationText = (specialization) => {
      const specializationTexts = {
        'ANXIETY_DEPRESSION': '焦虑抑郁',
        'RELATIONSHIP': '情感关系',
        'FAMILY_THERAPY': '家庭治疗',
        'CHILD_ADOLESCENT': '儿童青少年',
        'CAREER_DEVELOPMENT': '职业发展',
        'TRAUMA_PTSD': '创伤与PTSD',
        'ADDICTION': '成瘾治疗',
        'OTHER': '其他'
      }
      return specializationTexts[specialization] || specialization
    }

    // 获取咨询方式价格
    const getTypePrice = (type) => {
      if (!counselor.value) return 0
      const typeConfig = consultationTypes.find(t => t.value === type)
      return Math.round(counselor.value.hourlyRate * (typeConfig?.priceMultiplier || 1))
    }

    // 更新价格
    const updatePrice = () => {
      // 价格会通过计算属性自动更新
    }

    // 加载咨询师信息
    const loadCounselor = async () => {
      const counselorId = route.params.counselorId
      if (!counselorId) {
        router.push('/counselors')
        return
      }

      loading.value = true
      try {
        // TODO: 调用API获取咨询师信息
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        // 模拟数据
        counselor.value = {
          id: counselorId,
          name: '张心理师',
          specialization: 'ANXIETY_DEPRESSION',
          rating: 4.8,
          reviewCount: 156,
          hourlyRate: 200,
          professionalSummary: '专业心理咨询师，擅长焦虑症、抑郁症的认知行为治疗，具有丰富的临床经验。',
          avatarUrl: ''
        }
      } catch (error) {
        ElMessage.error('加载咨询师信息失败')
        counselor.value = null
      } finally {
        loading.value = false
      }
    }

    // 加载可预约时间段
    const loadAvailableSlots = async () => {
      if (!form.appointmentDate) return

      loadingSlots.value = true
      try {
        // TODO: 调用API获取可预约时间段
        await new Promise(resolve => setTimeout(resolve, 500))
        
        // 模拟数据
        availableSlots.value = [
          { time: '09:00' },
          { time: '10:00' },
          { time: '11:00' },
          { time: '14:00' },
          { time: '15:00' },
          { time: '16:00' },
          { time: '19:00' },
          { time: '20:00' }
        ]
        
        // 清空之前选择的时间
        form.appointmentTime = ''
      } catch (error) {
        ElMessage.error('加载可预约时间失败')
        availableSlots.value = []
      } finally {
        loadingSlots.value = false
      }
    }

    // 提交预约
    const handleSubmit = async () => {
      try {
        await ElMessageBox.confirm(
          `确认预约 ${counselor.value.name} 的咨询服务？\n时间：${form.appointmentDate} ${form.appointmentTime}\n费用：¥${totalAmount.value}`,
          '确认预约',
          {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            type: 'info'
          }
        )

        submitting.value = true

        // TODO: 调用API提交预约
        await new Promise(resolve => setTimeout(resolve, 2000))

        ElMessage.success('预约提交成功！')
        router.push('/dashboard/appointments')
      } catch (error) {
        if (error !== 'cancel') {
          ElMessage.error('预约提交失败，请重试')
        }
      } finally {
        submitting.value = false
      }
    }

    // 返回
    const goBack = () => {
      router.go(-1)
    }

    onMounted(() => {
      loadCounselor()
    })

    return {
      loading,
      submitting,
      loadingSlots,
      counselor,
      availableSlots,
      form,
      consultationTypes,
      durationOptions,
      minDate,
      maxDate,
      totalAmount,
      isFormValid,
      getSpecializationText,
      getTypePrice,
      updatePrice,
      loadAvailableSlots,
      handleSubmit,
      goBack
    }
  }
}
</script>

<style scoped>
.booking-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 24px;
}

.booking-header {
  text-align: center;
  margin-bottom: 32px;
}

.booking-header h2 {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8px;
}

.subtitle {
  color: #6b7280;
  font-size: 14px;
}

.loading,
.error-state {
  text-align: center;
  padding: 48px;
}

.error-state h3 {
  color: #dc2626;
  margin-bottom: 8px;
}

.error-state p {
  color: #6b7280;
  margin-bottom: 24px;
}

.booking-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.counselor-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 24px;
}

.counselor-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
}

.counselor-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
}

.counselor-info h3 {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.specialization {
  color: #6b7280;
  margin-bottom: 8px;
}

.rating {
  color: #6b7280;
  font-size: 14px;
  margin-bottom: 8px;
}

.stars {
  color: #fbbf24;
}

.price {
  display: flex;
  align-items: baseline;
  gap: 4px;
}

.amount {
  font-size: 24px;
  font-weight: 600;
  color: #dc2626;
}

.unit {
  color: #6b7280;
  font-size: 14px;
}

.counselor-summary p {
  color: #6b7280;
  line-height: 1.6;
}

.booking-form {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 24px;
}

.form-section {
  margin-bottom: 32px;
}

.form-section h4 {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 16px;
}

.consultation-types {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.type-option {
  display: flex;
  align-items: center;
  padding: 16px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
}

.type-option:hover {
  border-color: #3b82f6;
}

.type-option.active {
  border-color: #3b82f6;
  background: #eff6ff;
}

.type-option input[type="radio"] {
  margin-right: 12px;
}

.type-content {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.type-icon {
  font-size: 24px;
}

.type-info h5 {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.type-info p {
  color: #6b7280;
  font-size: 14px;
  margin-bottom: 4px;
}

.type-price {
  color: #dc2626;
  font-weight: 600;
  font-size: 14px;
}

.date-selection {
  margin-bottom: 20px;
}

.date-selection label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #374151;
}

.form-input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
}

.time-slots h5 {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 12px;
}

.loading-slots,
.no-slots {
  text-align: center;
  padding: 24px;
  color: #6b7280;
}

.slot-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 8px;
}

.slot-option {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
}

.slot-option:hover {
  border-color: #3b82f6;
}

.slot-option.active {
  border-color: #3b82f6;
  background: #eff6ff;
}

.slot-option input[type="radio"] {
  display: none;
}

.slot-time {
  font-size: 14px;
  font-weight: 500;
}

.duration-options {
  display: flex;
  gap: 12px;
}

.duration-option {
  padding: 8px 16px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
}

.duration-option:hover {
  border-color: #3b82f6;
}

.duration-option.active {
  border-color: #3b82f6;
  background: #eff6ff;
}

.duration-option input[type="radio"] {
  display: none;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: #374151;
}

.form-textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  resize: vertical;
}

.form-hint {
  display: block;
  margin-top: 4px;
  color: #6b7280;
  font-size: 12px;
}

.cost-summary {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 24px;
}

.cost-summary h4 {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 12px;
}

.cost-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.cost-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.cost-item.total {
  border-top: 1px solid #e5e7eb;
  padding-top: 8px;
  margin-top: 8px;
  font-weight: 600;
}

.cost-item .label {
  color: #374151;
}

.cost-item .value {
  color: #1f2937;
}

.cost-item.total .value {
  color: #dc2626;
  font-size: 18px;
}

.form-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
}

.btn {
  padding: 12px 24px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
  text-decoration: none;
  display: inline-block;
}

.btn-large {
  padding: 14px 32px;
  font-size: 16px;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #2563eb;
}

.btn-secondary {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn-secondary:hover {
  background: #e5e7eb;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
</style>
