-- ================================
-- 心理咨询服务平台 - 完整数据库初始化脚本
-- 包含：表结构、索引、视图、示例数据
-- ================================

-- 创建数据库
CREATE DATABASE IF NOT EXISTS psychology_platform 
CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE psychology_platform;

-- ================================
-- 1. 创建表结构
-- ================================

-- 用户表
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    email VARCHAR(100) NOT NULL UNIQUE COMMENT '邮箱',
    password VARCHAR(255) NOT NULL COMMENT '密码（加密）',
    real_name VARCHAR(100) COMMENT '真实姓名',
    phone VARCHAR(20) COMMENT '手机号',
    gender ENUM('MALE', 'FEMALE', 'OTHER') COMMENT '性别',
    birth_date DATE COMMENT '出生日期',
    avatar_url VARCHAR(500) COMMENT '头像URL',
    bio TEXT COMMENT '个人简介',
    status ENUM('ACTIVE', 'INACTIVE', 'BANNED') DEFAULT 'ACTIVE' COMMENT '用户状态',
    email_verified BOOLEAN DEFAULT FALSE COMMENT '邮箱是否验证',
    phone_verified BOOLEAN DEFAULT FALSE COMMENT '手机是否验证',
    last_login_time DATETIME COMMENT '最后登录时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_phone (phone),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) COMMENT '用户表';

-- 咨询师表
CREATE TABLE counselors (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '关联用户ID',
    license_number VARCHAR(100) NOT NULL UNIQUE COMMENT '执业证书编号',
    specialization ENUM(
        'ANXIETY_DISORDERS', 'DEPRESSION', 'RELATIONSHIP_COUNSELING', 
        'FAMILY_THERAPY', 'CHILD_PSYCHOLOGY', 'ADDICTION_COUNSELING',
        'TRAUMA_THERAPY', 'CAREER_COUNSELING', 'STRESS_MANAGEMENT', 
        'GENERAL_COUNSELING'
    ) NOT NULL COMMENT '专业领域',
    professional_summary TEXT COMMENT '专业简介',
    education VARCHAR(500) COMMENT '教育背景',
    years_of_experience INT DEFAULT 0 COMMENT '从业年限',
    hourly_rate DECIMAL(10,2) COMMENT '咨询费率（每小时）',
    status ENUM('PENDING', 'APPROVED', 'REJECTED', 'SUSPENDED') DEFAULT 'PENDING' COMMENT '认证状态',
    available_for_booking BOOLEAN DEFAULT FALSE COMMENT '是否可预约',
    rating DECIMAL(3,2) DEFAULT 0.00 COMMENT '平均评分',
    total_sessions INT DEFAULT 0 COMMENT '总咨询次数',
    verified_at DATETIME COMMENT '认证通过时间',
    rejection_reason TEXT COMMENT '拒绝原因',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '申请时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_license_number (license_number),
    INDEX idx_specialization (specialization),
    INDEX idx_status (status),
    INDEX idx_available (available_for_booking),
    INDEX idx_rating (rating),
    INDEX idx_hourly_rate (hourly_rate)
) COMMENT '咨询师表';

-- 预约表
CREATE TABLE appointments (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    counselor_id BIGINT NOT NULL COMMENT '咨询师ID',
    scheduled_time DATETIME NOT NULL COMMENT '预约时间',
    actual_start_time DATETIME COMMENT '实际开始时间',
    actual_end_time DATETIME COMMENT '实际结束时间',
    fee DECIMAL(10,2) COMMENT '咨询费用',
    notes TEXT COMMENT '预约备注',
    status ENUM('PENDING', 'CONFIRMED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED', 'NO_SHOW') 
           DEFAULT 'PENDING' COMMENT '预约状态',
    confirmed_at DATETIME COMMENT '确认时间',
    cancelled_at DATETIME COMMENT '取消时间',
    cancellation_reason TEXT COMMENT '取消原因',
    cancelled_by ENUM('USER', 'COUNSELOR', 'SYSTEM') COMMENT '取消方',
    reminder_sent BOOLEAN DEFAULT FALSE COMMENT '是否已发送提醒',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (counselor_id) REFERENCES counselors(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_counselor_id (counselor_id),
    INDEX idx_scheduled_time (scheduled_time),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    INDEX idx_user_scheduled (user_id, scheduled_time),
    INDEX idx_counselor_scheduled (counselor_id, scheduled_time)
) COMMENT '预约表';

-- 咨询会话表
CREATE TABLE counseling_sessions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    appointment_id BIGINT NOT NULL COMMENT '关联预约ID',
    start_time DATETIME COMMENT '开始时间',
    end_time DATETIME COMMENT '结束时间',
    session_notes TEXT COMMENT '会话记录',
    counselor_notes TEXT COMMENT '咨询师备注',
    user_feedback TEXT COMMENT '用户反馈',
    user_rating INT COMMENT '用户评分（1-5）',
    status ENUM('SCHEDULED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED') 
           DEFAULT 'SCHEDULED' COMMENT '会话状态',
    is_emergency BOOLEAN DEFAULT FALSE COMMENT '是否紧急情况',
    emergency_notes TEXT COMMENT '紧急情况备注',
    follow_up_required BOOLEAN DEFAULT FALSE COMMENT '是否需要跟进',
    follow_up_date DATETIME COMMENT '跟进日期',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (appointment_id) REFERENCES appointments(id) ON DELETE CASCADE,
    INDEX idx_appointment_id (appointment_id),
    INDEX idx_start_time (start_time),
    INDEX idx_status (status),
    INDEX idx_user_rating (user_rating),
    INDEX idx_emergency (is_emergency),
    INDEX idx_follow_up (follow_up_required, follow_up_date),
    
    CONSTRAINT chk_user_rating CHECK (user_rating >= 1 AND user_rating <= 5)
) COMMENT '咨询会话表';

-- 用户验证码表
CREATE TABLE verification_codes (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT COMMENT '用户ID（可为空，用于注册前验证）',
    email VARCHAR(100) COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '手机号',
    code VARCHAR(10) NOT NULL COMMENT '验证码',
    type ENUM('EMAIL_VERIFICATION', 'PHONE_VERIFICATION', 'PASSWORD_RESET') NOT NULL COMMENT '验证类型',
    used BOOLEAN DEFAULT FALSE COMMENT '是否已使用',
    expires_at DATETIME NOT NULL COMMENT '过期时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_email (email),
    INDEX idx_phone (phone),
    INDEX idx_code (code),
    INDEX idx_type (type),
    INDEX idx_expires_at (expires_at)
) COMMENT '验证码表';

-- 系统配置表
CREATE TABLE system_configs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    description VARCHAR(500) COMMENT '配置描述',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_config_key (config_key)
) COMMENT '系统配置表';

-- 操作日志表
CREATE TABLE operation_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT COMMENT '操作用户ID',
    operation_type VARCHAR(50) NOT NULL COMMENT '操作类型',
    operation_desc VARCHAR(500) COMMENT '操作描述',
    ip_address VARCHAR(50) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    request_url VARCHAR(500) COMMENT '请求URL',
    request_method VARCHAR(10) COMMENT '请求方法',
    request_params TEXT COMMENT '请求参数',
    response_status INT COMMENT '响应状态码',
    execution_time INT COMMENT '执行时间（毫秒）',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_operation_type (operation_type),
    INDEX idx_created_at (created_at),
    INDEX idx_ip_address (ip_address)
) COMMENT '操作日志表';

-- ================================
-- 2. 创建性能优化索引
-- ================================

-- 用户登录查询优化
CREATE INDEX idx_users_login ON users(username, password);
CREATE INDEX idx_users_email_login ON users(email, password);
CREATE INDEX idx_users_status_active ON users(status, last_login_time);

-- 咨询师搜索和筛选优化
CREATE INDEX idx_counselors_search ON counselors(status, available_for_booking, specialization, rating DESC);
CREATE INDEX idx_counselors_price_range ON counselors(hourly_rate, status, available_for_booking);
CREATE INDEX idx_counselors_experience ON counselors(years_of_experience, status, available_for_booking);
CREATE INDEX idx_counselors_popular ON counselors(total_sessions DESC, rating DESC, status);
CREATE INDEX idx_counselors_top_rated ON counselors(rating DESC, total_sessions DESC, status);

-- 预约查询优化
CREATE INDEX idx_appointments_user_time ON appointments(user_id, scheduled_time DESC, status);
CREATE INDEX idx_appointments_counselor_time ON appointments(counselor_id, scheduled_time ASC, status);
CREATE INDEX idx_appointments_today ON appointments(user_id, scheduled_time, status);
CREATE INDEX idx_appointments_counselor_today ON appointments(counselor_id, scheduled_time, status);
CREATE INDEX idx_appointments_upcoming ON appointments(scheduled_time, status, reminder_sent);
CREATE INDEX idx_appointments_conflict ON appointments(counselor_id, scheduled_time, actual_end_time, status);

-- 会话查询优化
CREATE INDEX idx_sessions_user_history ON counseling_sessions(appointment_id, start_time DESC, status);
CREATE INDEX idx_sessions_counselor_history ON counseling_sessions(start_time DESC, status);
CREATE INDEX idx_sessions_follow_up ON counseling_sessions(follow_up_required, follow_up_date, status);
CREATE INDEX idx_sessions_emergency ON counseling_sessions(is_emergency, start_time DESC);
CREATE INDEX idx_sessions_no_rating ON counseling_sessions(status, user_rating, end_time DESC);

-- 验证码查询优化
CREATE INDEX idx_verification_active ON verification_codes(email, phone, code, type, used, expires_at);
CREATE INDEX idx_verification_cleanup ON verification_codes(expires_at, used);

-- 操作日志查询优化
CREATE INDEX idx_logs_user_ops ON operation_logs(user_id, created_at DESC, operation_type);
CREATE INDEX idx_logs_monitoring ON operation_logs(operation_type, response_status, created_at DESC);

-- ================================
-- 3. 创建业务视图
-- ================================

-- 用户基本信息视图（隐藏敏感信息）
CREATE OR REPLACE VIEW v_user_profile AS
SELECT
    u.id,
    u.username,
    u.real_name,
    u.gender,
    u.birth_date,
    YEAR(CURDATE()) - YEAR(u.birth_date) -
    (DATE_FORMAT(CURDATE(), '%m%d') < DATE_FORMAT(u.birth_date, '%m%d')) AS age,
    u.avatar_url,
    u.bio,
    u.status,
    u.email_verified,
    u.phone_verified,
    u.last_login_time,
    u.created_at,
    CASE WHEN c.id IS NOT NULL THEN TRUE ELSE FALSE END AS is_counselor,
    c.id AS counselor_id,
    c.status AS counselor_status
FROM users u
LEFT JOIN counselors c ON u.id = c.user_id;

-- 咨询师详细信息视图
CREATE OR REPLACE VIEW v_counselor_detail AS
SELECT
    c.id,
    c.user_id,
    u.username,
    u.real_name,
    u.gender,
    u.avatar_url,
    u.bio AS user_bio,
    c.license_number,
    c.specialization,
    c.professional_summary,
    c.education,
    c.years_of_experience,
    c.hourly_rate,
    c.status,
    c.available_for_booking,
    c.rating,
    c.total_sessions,
    c.verified_at,
    c.created_at,
    CASE c.specialization
        WHEN 'ANXIETY_DISORDERS' THEN '焦虑障碍'
        WHEN 'DEPRESSION' THEN '抑郁症'
        WHEN 'RELATIONSHIP_COUNSELING' THEN '情感咨询'
        WHEN 'FAMILY_THERAPY' THEN '家庭治疗'
        WHEN 'CHILD_PSYCHOLOGY' THEN '儿童心理'
        WHEN 'ADDICTION_COUNSELING' THEN '成瘾咨询'
        WHEN 'TRAUMA_THERAPY' THEN '创伤治疗'
        WHEN 'CAREER_COUNSELING' THEN '职业咨询'
        WHEN 'STRESS_MANAGEMENT' THEN '压力管理'
        WHEN 'GENERAL_COUNSELING' THEN '综合咨询'
        ELSE c.specialization
    END AS specialization_name,
    CASE c.status
        WHEN 'PENDING' THEN '待审核'
        WHEN 'APPROVED' THEN '已认证'
        WHEN 'REJECTED' THEN '已拒绝'
        WHEN 'SUSPENDED' THEN '已暂停'
        ELSE c.status
    END AS status_name
FROM counselors c
JOIN users u ON c.user_id = u.id;

-- 可预约咨询师视图
CREATE OR REPLACE VIEW v_available_counselors AS
SELECT *
FROM v_counselor_detail
WHERE status = 'APPROVED' AND available_for_booking = TRUE;

-- 预约详细信息视图
CREATE OR REPLACE VIEW v_appointment_detail AS
SELECT
    a.id,
    a.user_id,
    a.counselor_id,
    a.scheduled_time,
    a.actual_start_time,
    a.actual_end_time,
    a.fee,
    a.notes,
    a.status,
    a.confirmed_at,
    a.cancelled_at,
    a.cancellation_reason,
    a.cancelled_by,
    a.created_at,
    u.username AS user_username,
    u.real_name AS user_real_name,
    u.phone AS user_phone,
    cu.username AS counselor_username,
    cu.real_name AS counselor_real_name,
    c.specialization,
    c.license_number,
    CASE a.status
        WHEN 'PENDING' THEN '待确认'
        WHEN 'CONFIRMED' THEN '已确认'
        WHEN 'IN_PROGRESS' THEN '进行中'
        WHEN 'COMPLETED' THEN '已完成'
        WHEN 'CANCELLED' THEN '已取消'
        WHEN 'NO_SHOW' THEN '未出席'
        ELSE a.status
    END AS status_name,
    CASE
        WHEN a.actual_start_time IS NOT NULL AND a.actual_end_time IS NOT NULL
        THEN TIMESTAMPDIFF(MINUTE, a.actual_start_time, a.actual_end_time)
        ELSE 60
    END AS duration_minutes,
    DATE(a.scheduled_time) = CURDATE() AS is_today,
    a.scheduled_time BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL 1 HOUR) AS is_upcoming
FROM appointments a
JOIN users u ON a.user_id = u.id
JOIN counselors c ON a.counselor_id = c.id
JOIN users cu ON c.user_id = cu.id;

-- 平台整体统计视图
CREATE OR REPLACE VIEW v_platform_stats AS
SELECT
    (SELECT COUNT(*) FROM users WHERE id NOT IN (SELECT DISTINCT user_id FROM counselors WHERE user_id IS NOT NULL)) AS total_users,
    (SELECT COUNT(*) FROM users WHERE status = 'ACTIVE' AND id NOT IN (SELECT DISTINCT user_id FROM counselors WHERE user_id IS NOT NULL)) AS active_users,
    (SELECT COUNT(*) FROM counselors) AS total_counselors,
    (SELECT COUNT(*) FROM counselors WHERE status = 'APPROVED') AS approved_counselors,
    (SELECT COUNT(*) FROM counselors WHERE status = 'APPROVED' AND available_for_booking = TRUE) AS available_counselors,
    (SELECT COUNT(*) FROM appointments) AS total_appointments,
    (SELECT COUNT(*) FROM appointments WHERE status = 'COMPLETED') AS completed_appointments,
    (SELECT COUNT(*) FROM appointments WHERE DATE(scheduled_time) = CURDATE()) AS appointments_today,
    (SELECT COUNT(*) FROM counseling_sessions) AS total_sessions,
    (SELECT COUNT(*) FROM counseling_sessions WHERE status = 'COMPLETED') AS completed_sessions,
    (SELECT AVG(user_rating) FROM counseling_sessions WHERE user_rating IS NOT NULL) AS avg_platform_rating,
    (SELECT SUM(fee) FROM appointments WHERE status = 'COMPLETED') AS total_revenue,
    (SELECT AVG(fee) FROM appointments WHERE status = 'COMPLETED') AS avg_session_fee;

-- ================================
-- 4. 插入系统配置数据
-- ================================

INSERT INTO system_configs (config_key, config_value, description) VALUES
('platform_name', '心理咨询服务平台', '平台名称'),
('max_appointment_advance_days', '30', '最大提前预约天数'),
('min_cancellation_hours', '24', '最小取消预约提前小时数'),
('default_session_duration', '60', '默认咨询时长（分钟）'),
('counselor_approval_required', 'true', '咨询师是否需要审核'),
('email_verification_required', 'true', '是否需要邮箱验证'),
('phone_verification_required', 'false', '是否需要手机验证'),
('verification_code_expiry_minutes', '10', '验证码有效期（分钟）'),
('max_login_attempts', '5', '最大登录尝试次数'),
('session_timeout_minutes', '30', '会话超时时间（分钟）');

-- ================================
-- 5. 插入示例数据（可选）
-- ================================

-- 插入示例用户数据
INSERT INTO users (username, email, password, real_name, phone, gender, birth_date, bio, status, email_verified, phone_verified) VALUES
-- 普通用户
('user001', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8ioctKk1o6tRkYjjOgqOzypfI8Fzu', '张三', '13800138001', 'MALE', '1990-05-15', '希望通过心理咨询改善生活质量', 'ACTIVE', TRUE, TRUE),
('user002', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8ioctKk1o6tRkYjjOgqOzypfI8Fzu', '李四', '13800138002', 'FEMALE', '1985-08-22', '关注心理健康，寻求专业帮助', 'ACTIVE', TRUE, FALSE),
('user003', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8ioctKk1o6tRkYjjOgqOzypfI8Fzu', '王五', '13800138003', 'MALE', '1992-12-03', '工作压力大，需要心理疏导', 'ACTIVE', TRUE, TRUE),
-- 咨询师用户
('counselor001', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8ioctKk1o6tRkYjjOgqOzypfI8Fzu', '陈心理', '13900139001', 'FEMALE', '1980-04-12', '专业心理咨询师，擅长焦虑和抑郁治疗', 'ACTIVE', TRUE, TRUE),
('counselor002', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8ioctKk1o6tRkYjjOgqOzypfI8Fzu', '刘医生', '13900139002', 'MALE', '1975-11-28', '资深心理治疗师，专注家庭治疗', 'ACTIVE', TRUE, TRUE),
('counselor003', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8ioctKk1o6tRkYjjOgqOzypfI8Fzu', '周咨询', '13900139003', 'FEMALE', '1983-09-14', '儿童心理专家，温和耐心', 'ACTIVE', TRUE, TRUE);

-- 插入咨询师数据
INSERT INTO counselors (user_id, license_number, specialization, professional_summary, education, years_of_experience, hourly_rate, status, available_for_booking, rating, total_sessions, verified_at) VALUES
(4, 'PSY20180001', 'ANXIETY_DISORDERS', '专注于焦虑障碍和抑郁症的认知行为治疗，拥有丰富的临床经验。', '北京大学心理学硕士', 8, 300.00, 'APPROVED', TRUE, 4.8, 156, '2023-01-15 10:30:00'),
(5, 'PSY20180002', 'FAMILY_THERAPY', '家庭系统治疗专家，擅长处理家庭关系冲突、亲子关系问题。', '清华大学应用心理学博士', 12, 450.00, 'APPROVED', TRUE, 4.9, 203, '2023-01-20 14:20:00'),
(6, 'PSY20180003', 'CHILD_PSYCHOLOGY', '儿童青少年心理专家，专业处理学习困难、行为问题、情绪调节等。', '华东师范大学发展心理学硕士', 6, 280.00, 'APPROVED', TRUE, 4.7, 89, '2023-02-01 09:15:00');

-- 插入预约数据
INSERT INTO appointments (user_id, counselor_id, scheduled_time, actual_start_time, actual_end_time, fee, notes, status, confirmed_at) VALUES
-- 已完成的预约
(1, 1, '2024-01-15 10:00:00', '2024-01-15 10:05:00', '2024-01-15 11:00:00', 300.00, '第一次咨询，主要了解焦虑症状', 'COMPLETED', '2024-01-14 15:30:00'),
(1, 1, '2024-01-22 10:00:00', '2024-01-22 10:00:00', '2024-01-22 11:00:00', 300.00, '继续焦虑治疗，讨论应对策略', 'COMPLETED', '2024-01-21 16:20:00'),
(2, 2, '2024-01-18 14:00:00', '2024-01-18 14:00:00', '2024-01-18 15:00:00', 450.00, '家庭关系咨询，夫妻沟通问题', 'COMPLETED', '2024-01-17 10:15:00'),
-- 即将进行的预约
(1, 1, '2024-02-05 10:00:00', NULL, NULL, 300.00, '第三次咨询，评估治疗进展', 'CONFIRMED', '2024-02-04 09:30:00'),
(2, 2, '2024-02-06 14:00:00', NULL, NULL, 450.00, '家庭治疗第二次，全家参与', 'CONFIRMED', '2024-02-05 16:20:00'),
-- 待确认的预约
(3, 3, '2024-02-08 10:00:00', NULL, NULL, 280.00, '儿童心理咨询，学习压力问题', 'PENDING', NULL);

-- 插入咨询会话数据
INSERT INTO counseling_sessions (appointment_id, start_time, end_time, session_notes, counselor_notes, user_feedback, user_rating, status, follow_up_required, follow_up_date) VALUES
(1, '2024-01-15 10:05:00', '2024-01-15 11:00:00',
 '来访者主诉工作压力导致的焦虑症状，包括失眠、心悸、担忧等。通过初步评估，确定为广泛性焦虑障碍。',
 '来访者配合度良好，有强烈的治疗动机。建议进行系统的CBT治疗。',
 '咨询师很专业，让我感到被理解和支持。期待下次咨询。',
 5, 'COMPLETED', TRUE, '2024-01-22 10:00:00'),
(2, '2024-01-22 10:00:00', '2024-01-22 11:00:00',
 '继续CBT治疗，教授放松技巧和认知重构方法。来访者报告焦虑症状有所缓解。',
 '治疗进展良好，来访者掌握了基本的应对技巧。继续强化练习。',
 '学到了很多实用的方法，焦虑确实有所减轻。',
 5, 'COMPLETED', TRUE, '2024-02-05 10:00:00'),
(3, '2024-01-18 14:00:00', '2024-01-18 15:00:00',
 '夫妻双方就沟通模式进行了深入探讨。识别了冲突的根源和各自的需求。',
 '夫妻关系紧张主要源于沟通不良。需要进一步的家庭治疗。',
 '第一次和爱人一起咨询，感觉很有帮助。开始理解对方的想法。',
 4, 'COMPLETED', TRUE, '2024-02-06 14:00:00');

-- 更新用户最后登录时间
UPDATE users SET last_login_time = NOW() WHERE id IN (1, 2, 3, 4, 5, 6);

-- ================================
-- 数据库初始化完成
-- ================================
