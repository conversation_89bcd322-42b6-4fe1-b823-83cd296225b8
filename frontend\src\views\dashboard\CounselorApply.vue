<template>
  <div class="counselor-apply-container">
    <div class="apply-header">
      <h2>申请成为咨询师</h2>
      <p class="subtitle">加入我们的专业咨询师团队，帮助更多需要心理支持的人</p>
    </div>

    <!-- 申请状态显示 -->
    <div v-if="applicationStatus" class="status-card" :class="getStatusClass(applicationStatus)">
      <div class="status-icon">
        <span v-if="applicationStatus === 'PENDING'">⏳</span>
        <span v-else-if="applicationStatus === 'APPROVED'">✅</span>
        <span v-else-if="applicationStatus === 'REJECTED'">❌</span>
      </div>
      <div class="status-content">
        <h3>{{ getStatusTitle(applicationStatus) }}</h3>
        <p>{{ getStatusDescription(applicationStatus) }}</p>
        <div v-if="applicationStatus === 'REJECTED' && rejectionReason" class="rejection-reason">
          <strong>拒绝原因：</strong>{{ rejectionReason }}
        </div>
      </div>
    </div>

    <!-- 申请表单 -->
    <div v-if="!applicationStatus || applicationStatus === 'REJECTED'" class="apply-form-section">
      <div class="form-header">
        <h3>{{ applicationStatus === 'REJECTED' ? '重新申请' : '填写申请信息' }}</h3>
        <p>请如实填写以下信息，我们将在3-5个工作日内完成审核</p>
      </div>

      <form @submit.prevent="handleSubmit" class="apply-form">
        <!-- 基本信息 -->
        <div class="form-section">
          <h4>基本信息</h4>
          
          <div class="form-group">
            <label for="licenseNumber">执业证书编号 *</label>
            <input 
              id="licenseNumber"
              v-model="form.licenseNumber" 
              type="text" 
              class="form-input"
              placeholder="请输入心理咨询师执业证书编号"
              required
            >
            <small class="form-hint">请确保证书编号真实有效，我们将进行核实</small>
          </div>

          <div class="form-group">
            <label for="specialization">专业领域 *</label>
            <select id="specialization" v-model="form.specialization" class="form-select" required>
              <option value="">请选择专业领域</option>
              <option value="ANXIETY_DEPRESSION">焦虑抑郁</option>
              <option value="RELATIONSHIP">情感关系</option>
              <option value="FAMILY_THERAPY">家庭治疗</option>
              <option value="CHILD_ADOLESCENT">儿童青少年</option>
              <option value="CAREER_DEVELOPMENT">职业发展</option>
              <option value="TRAUMA_PTSD">创伤与PTSD</option>
              <option value="ADDICTION">成瘾治疗</option>
              <option value="OTHER">其他</option>
            </select>
          </div>

          <div class="form-group">
            <label for="yearsOfExperience">从业年限 *</label>
            <select id="yearsOfExperience" v-model="form.yearsOfExperience" class="form-select" required>
              <option value="">请选择从业年限</option>
              <option value="1">1年以下</option>
              <option value="2">1-3年</option>
              <option value="5">3-5年</option>
              <option value="10">5-10年</option>
              <option value="15">10年以上</option>
            </select>
          </div>

          <div class="form-group">
            <label for="hourlyRate">咨询费用（元/小时）*</label>
            <input 
              id="hourlyRate"
              v-model="form.hourlyRate" 
              type="number" 
              class="form-input"
              placeholder="请输入您的咨询费用"
              min="50"
              max="2000"
              required
            >
            <small class="form-hint">建议费用范围：50-2000元/小时</small>
          </div>
        </div>

        <!-- 教育背景 -->
        <div class="form-section">
          <h4>教育背景</h4>
          
          <div class="form-group">
            <label for="education">最高学历 *</label>
            <select id="education" v-model="form.education" class="form-select" required>
              <option value="">请选择学历</option>
              <option value="BACHELOR">本科</option>
              <option value="MASTER">硕士</option>
              <option value="DOCTOR">博士</option>
            </select>
          </div>

          <div class="form-group">
            <label for="university">毕业院校 *</label>
            <input 
              id="university"
              v-model="form.university" 
              type="text" 
              class="form-input"
              placeholder="请输入毕业院校名称"
              required
            >
          </div>

          <div class="form-group">
            <label for="major">专业 *</label>
            <input 
              id="major"
              v-model="form.major" 
              type="text" 
              class="form-input"
              placeholder="请输入所学专业"
              required
            >
          </div>
        </div>

        <!-- 专业描述 -->
        <div class="form-section">
          <h4>专业描述</h4>
          
          <div class="form-group">
            <label for="professionalSummary">个人简介 *</label>
            <textarea 
              id="professionalSummary"
              v-model="form.professionalSummary" 
              class="form-textarea"
              placeholder="请详细介绍您的专业背景、治疗理念、擅长领域等（建议200-500字）"
              rows="6"
              required
            ></textarea>
            <small class="form-hint">{{ form.professionalSummary.length }}/500字</small>
          </div>

          <div class="form-group">
            <label for="workExperience">工作经历</label>
            <textarea 
              id="workExperience"
              v-model="form.workExperience" 
              class="form-textarea"
              placeholder="请描述您的相关工作经历（可选）"
              rows="4"
            ></textarea>
          </div>

          <div class="form-group">
            <label for="achievements">专业成就</label>
            <textarea 
              id="achievements"
              v-model="form.achievements" 
              class="form-textarea"
              placeholder="请列举您的专业成就、获奖情况、发表论文等（可选）"
              rows="4"
            ></textarea>
          </div>
        </div>

        <!-- 证书上传 -->
        <div class="form-section">
          <h4>证书上传</h4>
          
          <div class="form-group">
            <label>执业证书 *</label>
            <div class="upload-area" @click="uploadLicense">
              <div v-if="!form.licenseFile" class="upload-placeholder">
                <div class="upload-icon">📄</div>
                <p>点击上传执业证书</p>
                <small>支持 JPG、PNG、PDF 格式，大小不超过 5MB</small>
              </div>
              <div v-else class="upload-preview">
                <div class="file-info">
                  <span class="file-name">{{ form.licenseFile.name }}</span>
                  <button type="button" @click.stop="removeLicense" class="remove-btn">×</button>
                </div>
              </div>
            </div>
          </div>

          <div class="form-group">
            <label>学历证书</label>
            <div class="upload-area" @click="uploadDiploma">
              <div v-if="!form.diplomaFile" class="upload-placeholder">
                <div class="upload-icon">🎓</div>
                <p>点击上传学历证书（可选）</p>
                <small>支持 JPG、PNG、PDF 格式，大小不超过 5MB</small>
              </div>
              <div v-else class="upload-preview">
                <div class="file-info">
                  <span class="file-name">{{ form.diplomaFile.name }}</span>
                  <button type="button" @click.stop="removeDiploma" class="remove-btn">×</button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 协议确认 -->
        <div class="form-section">
          <div class="agreement-section">
            <label class="checkbox-label">
              <input type="checkbox" v-model="form.agreeToTerms" required>
              <span class="checkmark"></span>
              我已阅读并同意 <a href="#" class="link">《咨询师服务协议》</a> 和 <a href="#" class="link">《平台使用条款》</a>
            </label>
          </div>
        </div>

        <!-- 提交按钮 -->
        <div class="form-actions">
          <button type="submit" class="btn btn-primary btn-large" :disabled="loading">
            <span v-if="loading">提交中...</span>
            <span v-else>提交申请</span>
          </button>
          <button type="button" class="btn btn-secondary btn-large" @click="resetForm">
            重置表单
          </button>
        </div>
      </form>
    </div>

    <!-- 已通过审核的咨询师信息 -->
    <div v-if="applicationStatus === 'APPROVED'" class="counselor-info-section">
      <h3>咨询师信息</h3>
      <div class="counselor-card">
        <div class="counselor-header">
          <img :src="counselorInfo.avatarUrl || '/default-avatar.png'" alt="头像" class="counselor-avatar">
          <div class="counselor-details">
            <h4>{{ counselorInfo.name }}</h4>
            <p class="specialization">{{ getSpecializationText(counselorInfo.specialization) }}</p>
            <p class="rating">
              <span class="stars">⭐</span>
              {{ counselorInfo.rating }} ({{ counselorInfo.reviewCount }}条评价)
            </p>
          </div>
        </div>
        
        <div class="counselor-stats">
          <div class="stat-item">
            <span class="label">咨询费用：</span>
            <span class="value">¥{{ counselorInfo.hourlyRate }}/小时</span>
          </div>
          <div class="stat-item">
            <span class="label">从业年限：</span>
            <span class="value">{{ counselorInfo.yearsOfExperience }}年</span>
          </div>
          <div class="stat-item">
            <span class="label">总咨询次数：</span>
            <span class="value">{{ counselorInfo.totalSessions }}次</span>
          </div>
        </div>

        <div class="counselor-actions">
          <router-link to="/dashboard/counselor-center" class="btn btn-primary">
            进入咨询师中心
          </router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

export default {
  name: 'CounselorApply',
  setup() {
    const loading = ref(false)
    const applicationStatus = ref('') // '', 'PENDING', 'APPROVED', 'REJECTED'
    const rejectionReason = ref('')

    // 申请表单
    const form = reactive({
      licenseNumber: '',
      specialization: '',
      yearsOfExperience: '',
      hourlyRate: '',
      education: '',
      university: '',
      major: '',
      professionalSummary: '',
      workExperience: '',
      achievements: '',
      licenseFile: null,
      diplomaFile: null,
      agreeToTerms: false
    })

    // 咨询师信息（审核通过后显示）
    const counselorInfo = reactive({
      name: '',
      specialization: '',
      rating: 0,
      reviewCount: 0,
      hourlyRate: 0,
      yearsOfExperience: 0,
      totalSessions: 0,
      avatarUrl: ''
    })

    // 获取状态样式类
    const getStatusClass = (status) => {
      const statusClasses = {
        'PENDING': 'status-pending',
        'APPROVED': 'status-approved',
        'REJECTED': 'status-rejected'
      }
      return statusClasses[status] || ''
    }

    // 获取状态标题
    const getStatusTitle = (status) => {
      const statusTitles = {
        'PENDING': '申请审核中',
        'APPROVED': '申请已通过',
        'REJECTED': '申请被拒绝'
      }
      return statusTitles[status] || ''
    }

    // 获取状态描述
    const getStatusDescription = (status) => {
      const statusDescriptions = {
        'PENDING': '您的咨询师申请正在审核中，我们将在3-5个工作日内完成审核并通知您结果。',
        'APPROVED': '恭喜！您的咨询师申请已通过审核，现在可以开始提供咨询服务了。',
        'REJECTED': '很抱歉，您的咨询师申请未通过审核。您可以根据拒绝原因修改后重新申请。'
      }
      return statusDescriptions[status] || ''
    }

    // 获取专业领域文本
    const getSpecializationText = (specialization) => {
      const specializationTexts = {
        'ANXIETY_DEPRESSION': '焦虑抑郁',
        'RELATIONSHIP': '情感关系',
        'FAMILY_THERAPY': '家庭治疗',
        'CHILD_ADOLESCENT': '儿童青少年',
        'CAREER_DEVELOPMENT': '职业发展',
        'TRAUMA_PTSD': '创伤与PTSD',
        'ADDICTION': '成瘾治疗',
        'OTHER': '其他'
      }
      return specializationTexts[specialization] || specialization
    }

    // 上传执业证书
    const uploadLicense = () => {
      const input = document.createElement('input')
      input.type = 'file'
      input.accept = '.jpg,.jpeg,.png,.pdf'
      input.onchange = (e) => {
        const file = e.target.files[0]
        if (file && file.size <= 5 * 1024 * 1024) {
          form.licenseFile = file
        } else {
          ElMessage.error('文件大小不能超过5MB')
        }
      }
      input.click()
    }

    // 移除执业证书
    const removeLicense = () => {
      form.licenseFile = null
    }

    // 上传学历证书
    const uploadDiploma = () => {
      const input = document.createElement('input')
      input.type = 'file'
      input.accept = '.jpg,.jpeg,.png,.pdf'
      input.onchange = (e) => {
        const file = e.target.files[0]
        if (file && file.size <= 5 * 1024 * 1024) {
          form.diplomaFile = file
        } else {
          ElMessage.error('文件大小不能超过5MB')
        }
      }
      input.click()
    }

    // 移除学历证书
    const removeDiploma = () => {
      form.diplomaFile = null
    }

    // 提交申请
    const handleSubmit = async () => {
      if (!form.licenseFile) {
        ElMessage.error('请上传执业证书')
        return
      }

      if (form.professionalSummary.length < 50) {
        ElMessage.error('个人简介至少需要50字')
        return
      }

      loading.value = true
      try {
        // TODO: 调用API提交申请
        await new Promise(resolve => setTimeout(resolve, 2000))
        
        applicationStatus.value = 'PENDING'
        ElMessage.success('申请提交成功，请等待审核结果')
      } catch (error) {
        ElMessage.error('申请提交失败，请重试')
      } finally {
        loading.value = false
      }
    }

    // 重置表单
    const resetForm = () => {
      Object.assign(form, {
        licenseNumber: '',
        specialization: '',
        yearsOfExperience: '',
        hourlyRate: '',
        education: '',
        university: '',
        major: '',
        professionalSummary: '',
        workExperience: '',
        achievements: '',
        licenseFile: null,
        diplomaFile: null,
        agreeToTerms: false
      })
    }

    // 检查申请状态
    const checkApplicationStatus = async () => {
      try {
        // TODO: 调用API检查申请状态
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        // 模拟不同状态
        // applicationStatus.value = 'PENDING'
        // applicationStatus.value = 'APPROVED'
        // applicationStatus.value = 'REJECTED'
        // rejectionReason.value = '执业证书编号无效，请核实后重新提交'
        
        if (applicationStatus.value === 'APPROVED') {
          // 加载咨询师信息
          Object.assign(counselorInfo, {
            name: '张心理师',
            specialization: 'ANXIETY_DEPRESSION',
            rating: 4.8,
            reviewCount: 156,
            hourlyRate: 200,
            yearsOfExperience: 5,
            totalSessions: 320,
            avatarUrl: ''
          })
        }
      } catch (error) {
        console.error('检查申请状态失败:', error)
      }
    }

    onMounted(() => {
      checkApplicationStatus()
    })

    return {
      loading,
      applicationStatus,
      rejectionReason,
      form,
      counselorInfo,
      getStatusClass,
      getStatusTitle,
      getStatusDescription,
      getSpecializationText,
      uploadLicense,
      removeLicense,
      uploadDiploma,
      removeDiploma,
      handleSubmit,
      resetForm
    }
  }
}
</script>

<style scoped>
.counselor-apply-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 24px;
}

.apply-header {
  margin-bottom: 32px;
  text-align: center;
}

.apply-header h2 {
  font-size: 28px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8px;
}

.subtitle {
  color: #6b7280;
  font-size: 16px;
}

.status-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  border-radius: 12px;
  margin-bottom: 32px;
}

.status-pending {
  background: #fef3c7;
  border: 1px solid #f59e0b;
}

.status-approved {
  background: #d1fae5;
  border: 1px solid #10b981;
}

.status-rejected {
  background: #fee2e2;
  border: 1px solid #ef4444;
}

.status-icon {
  font-size: 32px;
}

.status-content h3 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
}

.status-content p {
  margin-bottom: 8px;
}

.rejection-reason {
  color: #dc2626;
  font-size: 14px;
}

.apply-form-section {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 24px;
}

.form-header {
  margin-bottom: 32px;
  text-align: center;
}

.form-header h3 {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8px;
}

.form-header p {
  color: #6b7280;
}

.form-section {
  margin-bottom: 32px;
}

.form-section h4 {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #e5e7eb;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: #374151;
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-hint {
  display: block;
  margin-top: 4px;
  color: #6b7280;
  font-size: 12px;
}

.upload-area {
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: border-color 0.2s;
}

.upload-area:hover {
  border-color: #3b82f6;
}

.upload-placeholder {
  color: #6b7280;
}

.upload-icon {
  font-size: 32px;
  margin-bottom: 8px;
}

.upload-preview {
  display: flex;
  align-items: center;
  justify-content: center;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f3f4f6;
  border-radius: 6px;
}

.file-name {
  color: #374151;
  font-size: 14px;
}

.remove-btn {
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  cursor: pointer;
  font-size: 12px;
}

.agreement-section {
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
  margin: 0;
}

.link {
  color: #3b82f6;
  text-decoration: none;
}

.link:hover {
  text-decoration: underline;
}

.form-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  margin-top: 32px;
}

.btn {
  padding: 12px 24px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
  text-decoration: none;
  display: inline-block;
}

.btn-large {
  padding: 14px 32px;
  font-size: 16px;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #2563eb;
}

.btn-secondary {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn-secondary:hover {
  background: #e5e7eb;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.counselor-info-section {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 24px;
}

.counselor-info-section h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 20px;
}

.counselor-card {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
}

.counselor-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
}

.counselor-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
}

.counselor-details h4 {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.specialization {
  color: #6b7280;
  margin-bottom: 4px;
}

.rating {
  color: #6b7280;
  font-size: 14px;
}

.stars {
  color: #fbbf24;
}

.counselor-stats {
  margin-bottom: 20px;
}

.stat-item {
  display: flex;
  margin-bottom: 8px;
}

.stat-item .label {
  font-weight: 500;
  color: #374151;
  min-width: 100px;
}

.stat-item .value {
  color: #6b7280;
}

.counselor-actions {
  text-align: center;
}
</style>
