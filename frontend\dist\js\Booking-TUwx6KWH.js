import{c as a,a as o,d as U,w as z,e as L,y as l,n as E,A as F,F as y,k as g,h as H,i as m,Z as x,r as p,z as Y,S as _,o as X,L as q,l as Z,E as b,R as j,m as i,U as T,a0 as D}from"./index-D4DFMkO1.js";import{_ as G}from"./_plugin-vue_export-helper-DlAUqK2U.js";const J={name:"Booking",setup(){const S=q(),t=Z(),v=p(!1),s=p(!1),f=p(!1),d=p(null),c=p([]),n=Y({consultationType:"VIDEO",appointmentDate:"",appointmentTime:"",duration:60,notes:""}),r=[{value:"VIDEO",label:"视频咨询",description:"面对面视频交流，体验最佳",icon:"📹",priceMultiplier:1},{value:"VOICE",label:"语音咨询",description:"语音通话，保护隐私",icon:"🎙️",priceMultiplier:.9},{value:"TEXT",label:"文字咨询",description:"文字交流，随时回顾",icon:"💬",priceMultiplier:.8}],P=[{value:45,label:"45分钟"},{value:60,label:"60分钟"},{value:90,label:"90分钟"}],I=_(()=>{const e=new Date;return e.setDate(e.getDate()+1),e.toISOString().split("T")[0]}),w=_(()=>{const e=new Date;return e.setDate(e.getDate()+30),e.toISOString().split("T")[0]}),h=_(()=>{if(!d.value)return 0;const e=k(n.consultationType);return Math.round(e*(n.duration/60))}),A=_(()=>n.consultationType&&n.appointmentDate&&n.appointmentTime&&n.duration),V=e=>({ANXIETY_DEPRESSION:"焦虑抑郁",RELATIONSHIP:"情感关系",FAMILY_THERAPY:"家庭治疗",CHILD_ADOLESCENT:"儿童青少年",CAREER_DEVELOPMENT:"职业发展",TRAUMA_PTSD:"创伤与PTSD",ADDICTION:"成瘾治疗",OTHER:"其他"})[e]||e,k=e=>{if(!d.value)return 0;const u=r.find(B=>B.value===e);return Math.round(d.value.hourlyRate*((u==null?void 0:u.priceMultiplier)||1))},C=()=>{},M=async()=>{const e=S.params.counselorId;if(!e){t.push("/counselors");return}v.value=!0;try{await new Promise(u=>setTimeout(u,1e3)),d.value={id:e,name:"张心理师",specialization:"ANXIETY_DEPRESSION",rating:4.8,reviewCount:156,hourlyRate:200,professionalSummary:"专业心理咨询师，擅长焦虑症、抑郁症的认知行为治疗，具有丰富的临床经验。",avatarUrl:""}}catch{b.error("加载咨询师信息失败"),d.value=null}finally{v.value=!1}},R=async()=>{if(n.appointmentDate){f.value=!0;try{await new Promise(e=>setTimeout(e,500)),c.value=[{time:"09:00"},{time:"10:00"},{time:"11:00"},{time:"14:00"},{time:"15:00"},{time:"16:00"},{time:"19:00"},{time:"20:00"}],n.appointmentTime=""}catch{b.error("加载可预约时间失败"),c.value=[]}finally{f.value=!1}}},O=async()=>{try{await j.confirm(`确认预约 ${d.value.name} 的咨询服务？
时间：${n.appointmentDate} ${n.appointmentTime}
费用：¥${h.value}`,"确认预约",{confirmButtonText:"确认",cancelButtonText:"取消",type:"info"}),s.value=!0,await new Promise(e=>setTimeout(e,2e3)),b.success("预约提交成功！"),t.push("/dashboard/appointments")}catch(e){e!=="cancel"&&b.error("预约提交失败，请重试")}finally{s.value=!1}},N=()=>{t.go(-1)};return X(()=>{M()}),{loading:v,submitting:s,loadingSlots:f,counselor:d,availableSlots:c,form:n,consultationTypes:r,durationOptions:P,minDate:I,maxDate:w,totalAmount:h,isFormValid:A,getSpecializationText:V,getTypePrice:k,updatePrice:C,loadAvailableSlots:R,handleSubmit:O,goBack:N}}},K={class:"booking-container"},Q={key:0,class:"loading"},W={key:1,class:"error-state"},$={key:2,class:"booking-content"},oo={class:"counselor-card"},to={class:"counselor-header"},so=["src","alt"],no={class:"counselor-info"},eo={class:"specialization"},ao={class:"rating"},lo={class:"price"},io={class:"amount"},ro={class:"counselor-summary"},uo={class:"booking-form"},co={class:"form-section"},mo={class:"consultation-types"},po=["value"],vo={class:"type-content"},fo={class:"type-icon"},_o={class:"type-info"},bo={class:"type-price"},yo={class:"form-section"},go={class:"date-selection"},To=["min","max"],Do={key:0,class:"time-slots"},So={key:0,class:"loading-slots"},ho={key:1,class:"no-slots"},ko={key:2,class:"slot-grid"},Eo=["value"],xo={class:"slot-time"},Po={class:"form-section"},Io={class:"duration-options"},wo=["value","onUpdate:modelValue"],Ao={class:"duration-text"},Vo={class:"form-section"},Co={class:"form-group"},Mo={class:"form-hint"},Ro={class:"cost-summary"},Oo={class:"cost-details"},No={class:"cost-item"},Bo={class:"value"},Uo={class:"cost-item"},zo={class:"value"},Lo={class:"cost-item total"},Fo={class:"value"},Ho={class:"form-actions"},Yo=["disabled"],Xo={key:0},qo={key:1};function Zo(S,t,v,s,f,d){const c=L("router-link");return i(),a("div",K,[t[28]||(t[28]=o("div",{class:"booking-header"},[o("h2",null,"预约咨询"),o("p",{class:"subtitle"},"选择合适的时间，开始您的心理咨询之旅")],-1)),s.loading?(i(),a("div",Q,[...t[9]||(t[9]=[o("p",null,"加载中...",-1)])])):s.counselor?(i(),a("div",$,[o("div",oo,[o("div",to,[o("img",{src:s.counselor.avatarUrl||"/default-avatar.png",alt:s.counselor.name,class:"counselor-avatar"},null,8,so),o("div",no,[o("h3",null,l(s.counselor.name),1),o("p",eo,l(s.getSpecializationText(s.counselor.specialization)),1),o("div",ao,[t[13]||(t[13]=o("span",{class:"stars"},"⭐",-1)),E(" "+l(s.counselor.rating)+" ("+l(s.counselor.reviewCount)+"条评价) ",1)]),o("div",lo,[o("span",io,"¥"+l(s.counselor.hourlyRate),1),t[14]||(t[14]=o("span",{class:"unit"},"/小时",-1))])])]),o("div",ro,[o("p",null,l(s.counselor.professionalSummary),1)])]),o("div",uo,[o("form",{onSubmit:t[8]||(t[8]=F((...n)=>s.handleSubmit&&s.handleSubmit(...n),["prevent"]))},[o("div",co,[t[15]||(t[15]=o("h4",null,"选择咨询方式",-1)),o("div",mo,[(i(!0),a(y,null,g(s.consultationTypes,n=>(i(),a("label",{key:n.value,class:T(["type-option",{active:s.form.consultationType===n.value}])},[m(o("input",{type:"radio",value:n.value,"onUpdate:modelValue":t[0]||(t[0]=r=>s.form.consultationType=r),onChange:t[1]||(t[1]=(...r)=>s.updatePrice&&s.updatePrice(...r))},null,40,po),[[D,s.form.consultationType]]),o("div",vo,[o("div",fo,l(n.icon),1),o("div",_o,[o("h5",null,l(n.label),1),o("p",null,l(n.description),1),o("span",bo,"¥"+l(s.getTypePrice(n.value))+"/小时",1)])])],2))),128))])]),o("div",yo,[t[20]||(t[20]=o("h4",null,"选择咨询时间",-1)),o("div",go,[t[16]||(t[16]=o("label",{for:"appointmentDate"},"预约日期：",-1)),m(o("input",{id:"appointmentDate",type:"date","onUpdate:modelValue":t[2]||(t[2]=n=>s.form.appointmentDate=n),min:s.minDate,max:s.maxDate,onChange:t[3]||(t[3]=(...n)=>s.loadAvailableSlots&&s.loadAvailableSlots(...n)),class:"form-input",required:""},null,40,To),[[x,s.form.appointmentDate]])]),s.form.appointmentDate?(i(),a("div",Do,[t[19]||(t[19]=o("h5",null,"可预约时间段：",-1)),s.loadingSlots?(i(),a("div",So,[...t[17]||(t[17]=[o("p",null,"加载时间段中...",-1)])])):s.availableSlots.length===0?(i(),a("div",ho,[...t[18]||(t[18]=[o("p",null,"该日期暂无可预约时间段",-1)])])):(i(),a("div",ko,[(i(!0),a(y,null,g(s.availableSlots,n=>(i(),a("label",{key:n.time,class:T(["slot-option",{active:s.form.appointmentTime===n.time}])},[m(o("input",{type:"radio",value:n.time,"onUpdate:modelValue":t[4]||(t[4]=r=>s.form.appointmentTime=r)},null,8,Eo),[[D,s.form.appointmentTime]]),o("span",xo,l(n.time),1)],2))),128))]))])):H("",!0)]),o("div",Po,[t[21]||(t[21]=o("h4",null,"咨询时长",-1)),o("div",Io,[(i(!0),a(y,null,g(s.durationOptions,n=>(i(),a("label",{key:n.value,class:T(["duration-option",{active:s.form.duration===n.value}])},[m(o("input",{type:"radio",value:n.value,"onUpdate:modelValue":r=>s.form.duration=r,onChange:t[5]||(t[5]=(...r)=>s.updatePrice&&s.updatePrice(...r))},null,40,wo),[[D,s.form.duration]]),o("span",Ao,l(n.label),1)],2))),128))])]),o("div",Vo,[t[23]||(t[23]=o("h4",null,"备注信息",-1)),o("div",Co,[t[22]||(t[22]=o("label",{for:"notes"},"咨询需求描述（可选）：",-1)),m(o("textarea",{id:"notes","onUpdate:modelValue":t[6]||(t[6]=n=>s.form.notes=n),class:"form-textarea",placeholder:"请简要描述您希望咨询的问题或需求，这将帮助咨询师更好地为您服务...",rows:"4"},null,512),[[x,s.form.notes]]),o("small",Mo,l(s.form.notes.length)+"/500字",1)])]),o("div",Ro,[t[27]||(t[27]=o("h4",null,"费用明细",-1)),o("div",Oo,[o("div",No,[t[24]||(t[24]=o("span",{class:"label"},"咨询费用：",-1)),o("span",Bo,"¥"+l(s.getTypePrice(s.form.consultationType))+"/小时",1)]),o("div",Uo,[t[25]||(t[25]=o("span",{class:"label"},"咨询时长：",-1)),o("span",zo,l(s.form.duration)+"分钟",1)]),o("div",Lo,[t[26]||(t[26]=o("span",{class:"label"},"总费用：",-1)),o("span",Fo,"¥"+l(s.totalAmount),1)])])]),o("div",Ho,[o("button",{type:"submit",class:"btn btn-primary btn-large",disabled:s.submitting||!s.isFormValid},[s.submitting?(i(),a("span",Xo,"提交中...")):(i(),a("span",qo,"确认预约"))],8,Yo),o("button",{type:"button",class:"btn btn-secondary btn-large",onClick:t[7]||(t[7]=(...n)=>s.goBack&&s.goBack(...n))}," 返回 ")])],32)])])):(i(),a("div",W,[t[11]||(t[11]=o("h3",null,"咨询师信息加载失败",-1)),t[12]||(t[12]=o("p",null,"请返回重新选择咨询师",-1)),U(c,{to:"/counselors",class:"btn btn-primary"},{default:z(()=>[...t[10]||(t[10]=[E(" 返回咨询师列表 ",-1)])]),_:1})]))])}const Jo=G(J,[["render",Zo],["__scopeId","data-v-ab2617ff"]]);export{Jo as default};
