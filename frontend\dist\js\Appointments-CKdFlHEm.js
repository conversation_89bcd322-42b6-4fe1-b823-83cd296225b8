import{c as i,a as e,h as g,i as y,_ as w,b as k,d as V,w as F,e as G,F as z,k as B,y as l,r as c,S as U,o as j,E as u,R as Y,l as X,m as r,n as P,U as N}from"./index-D4DFMkO1.js";import{_ as q}from"./_plugin-vue_export-helper-DlAUqK2U.js";const H={name:"Appointments",setup(){X();const p=c(!1),t=c([]),f=c(""),n=c(""),C=c(1);c(10);const E=c(1),b=[{id:1,counselor:{name:"张心理师",specialization:"焦虑症治疗",rating:4.8,reviewCount:156,avatarUrl:""},scheduledTime:"2024-09-01T14:00:00",consultationType:"VIDEO",totalAmount:200,status:"CONFIRMED",notes:"希望能帮助缓解工作压力",hasReview:!1},{id:2,counselor:{name:"李咨询师",specialization:"情感咨询",rating:4.9,reviewCount:203,avatarUrl:""},scheduledTime:"2024-08-28T10:00:00",consultationType:"VOICE",totalAmount:180,status:"COMPLETED",notes:"",hasReview:!0}],s=U(()=>{let o=t.value;if(f.value&&(o=o.filter(a=>a.status===f.value)),n.value){const a=new Date;o=o.filter(m=>{const d=new Date(m.scheduledTime);switch(n.value){case"upcoming":return d>a;case"past":return d<a;case"today":return d.toDateString()===a.toDateString();case"week":const _=new Date(a.setDate(a.getDate()-a.getDay())),T=new Date(_);return T.setDate(_.getDate()+6),d>=_&&d<=T;case"month":return d.getMonth()===a.getMonth()&&d.getFullYear()===a.getFullYear();default:return!0}})}return o}),v=o=>({PENDING:"status-pending",CONFIRMED:"status-confirmed",IN_PROGRESS:"status-in-progress",COMPLETED:"status-completed",CANCELLED:"status-cancelled"})[o]||"",S=o=>({PENDING:"待确认",CONFIRMED:"已确认",IN_PROGRESS:"进行中",COMPLETED:"已完成",CANCELLED:"已取消"})[o]||o,A=o=>({VIDEO:"视频咨询",VOICE:"语音咨询",TEXT:"文字咨询"})[o]||o,R=o=>new Date(o).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"}),D=async()=>{p.value=!0;try{await new Promise(o=>setTimeout(o,1e3)),t.value=b}catch{u.error("加载预约数据失败")}finally{p.value=!1}},h=()=>{C.value=1},I=async o=>{try{await Y.confirm("确定要取消这个预约吗？","确认取消",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await new Promise(m=>setTimeout(m,1e3));const a=t.value.find(m=>m.id===o);a&&(a.status="CANCELLED"),u.success("预约已取消")}catch(a){a!=="cancel"&&u.error("取消预约失败")}},O=o=>{u.info("咨询会话功能开发中...")},x=o=>{u.info("评价功能开发中...")},M=o=>{u.info("详情页面开发中...")},L=o=>{o>=1&&o<=E.value&&(C.value=o,D())};return j(()=>{D()}),{loading:p,appointments:t,filteredAppointments:s,selectedStatus:f,selectedTimeRange:n,currentPage:C,totalPages:E,getStatusClass:v,getStatusText:S,getConsultationTypeText:A,formatDateTime:R,filterAppointments:h,cancelAppointment:I,joinSession:O,writeReview:x,viewDetails:M,changePage:L}}},J={class:"appointments-container"},K={class:"filters"},Q={class:"filter-group"},W={class:"filter-group"},Z={class:"appointments-list"},$={key:0,class:"loading"},tt={key:1,class:"empty-state"},et={key:2,class:"appointment-cards"},st={class:"counselor-info"},nt=["src","alt"],ot={class:"counselor-details"},at={class:"specialization"},it={class:"rating"},lt={class:"appointment-details"},rt={class:"detail-item"},dt={class:"value"},ct={class:"detail-item"},ut={class:"value"},vt={class:"detail-item"},mt={class:"value price"},gt={key:0,class:"detail-item"},pt={class:"value"},ft={class:"appointment-actions"},Ct=["onClick"],Et=["onClick"],bt=["onClick"],_t=["onClick"],Dt={key:0,class:"pagination"},Tt=["disabled"],yt={class:"page-info"},wt=["disabled"];function kt(p,t,f,n,C,E){const b=G("router-link");return r(),i("div",J,[t[20]||(t[20]=e("div",{class:"appointments-header"},[e("h2",null,"我的预约"),e("p",{class:"subtitle"},"查看和管理您的咨询预约")],-1)),e("div",K,[e("div",Q,[t[7]||(t[7]=e("label",null,"状态筛选：",-1)),y(e("select",{"onUpdate:modelValue":t[0]||(t[0]=s=>n.selectedStatus=s),onChange:t[1]||(t[1]=(...s)=>n.filterAppointments&&n.filterAppointments(...s)),class:"filter-select"},[...t[6]||(t[6]=[k('<option value="" data-v-a7fe5d04>全部状态</option><option value="PENDING" data-v-a7fe5d04>待确认</option><option value="CONFIRMED" data-v-a7fe5d04>已确认</option><option value="IN_PROGRESS" data-v-a7fe5d04>进行中</option><option value="COMPLETED" data-v-a7fe5d04>已完成</option><option value="CANCELLED" data-v-a7fe5d04>已取消</option>',6)])],544),[[w,n.selectedStatus]])]),e("div",W,[t[9]||(t[9]=e("label",null,"时间范围：",-1)),y(e("select",{"onUpdate:modelValue":t[2]||(t[2]=s=>n.selectedTimeRange=s),onChange:t[3]||(t[3]=(...s)=>n.filterAppointments&&n.filterAppointments(...s)),class:"filter-select"},[...t[8]||(t[8]=[k('<option value="" data-v-a7fe5d04>全部时间</option><option value="upcoming" data-v-a7fe5d04>即将到来</option><option value="past" data-v-a7fe5d04>历史记录</option><option value="today" data-v-a7fe5d04>今天</option><option value="week" data-v-a7fe5d04>本周</option><option value="month" data-v-a7fe5d04>本月</option>',6)])],544),[[w,n.selectedTimeRange]])])]),e("div",Z,[n.loading?(r(),i("div",$,[...t[10]||(t[10]=[e("p",null,"加载中...",-1)])])):n.filteredAppointments.length===0?(r(),i("div",tt,[t[12]||(t[12]=e("div",{class:"empty-icon"},"📅",-1)),t[13]||(t[13]=e("h3",null,"暂无预约记录",-1)),t[14]||(t[14]=e("p",null,"您还没有任何咨询预约",-1)),V(b,{to:"/counselors",class:"btn btn-primary"},{default:F(()=>[...t[11]||(t[11]=[P(" 立即预约咨询 ",-1)])]),_:1})])):(r(),i("div",et,[(r(!0),i(z,null,B(n.filteredAppointments,s=>(r(),i("div",{key:s.id,class:N(["appointment-card",n.getStatusClass(s.status)])},[e("div",{class:N(["status-badge",n.getStatusClass(s.status)])},l(n.getStatusText(s.status)),3),e("div",st,[e("img",{src:s.counselor.avatarUrl||"/default-avatar.png",alt:s.counselor.name,class:"counselor-avatar"},null,8,nt),e("div",ot,[e("h4",null,l(s.counselor.name),1),e("p",at,l(s.counselor.specialization),1),e("p",it,[t[15]||(t[15]=e("span",{class:"stars"},"⭐",-1)),P(" "+l(s.counselor.rating)+" ("+l(s.counselor.reviewCount)+"条评价) ",1)])])]),e("div",lt,[e("div",rt,[t[16]||(t[16]=e("span",{class:"label"},"预约时间：",-1)),e("span",dt,l(n.formatDateTime(s.scheduledTime)),1)]),e("div",ct,[t[17]||(t[17]=e("span",{class:"label"},"咨询方式：",-1)),e("span",ut,l(n.getConsultationTypeText(s.consultationType)),1)]),e("div",vt,[t[18]||(t[18]=e("span",{class:"label"},"费用：",-1)),e("span",mt,"¥"+l(s.totalAmount),1)]),s.notes?(r(),i("div",gt,[t[19]||(t[19]=e("span",{class:"label"},"备注：",-1)),e("span",pt,l(s.notes),1)])):g("",!0)]),e("div",ft,[s.status==="PENDING"?(r(),i("button",{key:0,onClick:v=>n.cancelAppointment(s.id),class:"btn btn-danger btn-sm"}," 取消预约 ",8,Ct)):g("",!0),s.status==="CONFIRMED"?(r(),i("button",{key:1,onClick:v=>n.joinSession(s.id),class:"btn btn-primary btn-sm"}," 进入咨询 ",8,Et)):g("",!0),s.status==="COMPLETED"&&!s.hasReview?(r(),i("button",{key:2,onClick:v=>n.writeReview(s.id),class:"btn btn-secondary btn-sm"}," 写评价 ",8,bt)):g("",!0),e("button",{onClick:v=>n.viewDetails(s.id),class:"btn btn-outline btn-sm"}," 查看详情 ",8,_t)])],2))),128))]))]),n.totalPages>1?(r(),i("div",Dt,[e("button",{onClick:t[4]||(t[4]=s=>n.changePage(n.currentPage-1)),disabled:n.currentPage===1,class:"btn btn-outline btn-sm"}," 上一页 ",8,Tt),e("span",yt," 第 "+l(n.currentPage)+" 页，共 "+l(n.totalPages)+" 页 ",1),e("button",{onClick:t[5]||(t[5]=s=>n.changePage(n.currentPage+1)),disabled:n.currentPage===n.totalPages,class:"btn btn-outline btn-sm"}," 下一页 ",8,wt)])):g("",!0)])}const St=q(H,[["render",kt],["__scopeId","data-v-a7fe5d04"]]);export{St as default};
