import{u as ne,r as g,z as oe,o as ie,c as p,a as l,d as t,w as s,e as d,f as E,F as z,i as re,h as F,j as de,k as G,E as M,l as ue,m as _,n as o,y as c,s as ce,I as pe,J as _e,A as H}from"./index-D4DFMkO1.js";import{s as me}from"./counselor-De_gGA3T.js";import{_ as ve}from"./_plugin-vue_export-helper-DlAUqK2U.js";const ge={class:"counselor-list-page"},fe={class:"navbar"},Ce={class:"nav-container"},ye={class:"nav-menu"},Re={class:"page-container"},be={class:"content-wrapper"},he={class:"search-section card"},ke={class:"price-display"},Se={class:"sort-section"},Ee={class:"counselors-section"},ze={class:"counselors-grid"},Ne=["onClick"],Ve={class:"counselor-header"},xe={class:"counselor-basic"},Ie={class:"specialization"},Le={class:"rating"},Ue={class:"sessions-count"},Ae={class:"counselor-price"},we={class:"price"},Ye={class:"counselor-info"},Oe={class:"info-item"},Te={class:"info-item"},De={class:"counselor-summary"},Pe={class:"counselor-actions"},Be={key:0,class:"empty-state"},Fe={key:0,class:"pagination-section"},Ge={__name:"CounselorList",setup(Me){const h=ue(),N=ne(),f=g(!1),V=g([]),x=g(0),m=g(1),k=g(12),I=g("rating"),n=oe({specialization:"",priceRange:[0,1e3],minRating:0,minYears:null}),U={ANXIETY_DISORDERS:"焦虑障碍",DEPRESSION:"抑郁症",RELATIONSHIP_COUNSELING:"情感咨询",FAMILY_THERAPY:"家庭治疗",CHILD_PSYCHOLOGY:"儿童心理",ADDICTION_COUNSELING:"成瘾咨询",TRAUMA_THERAPY:"创伤治疗",CAREER_COUNSELING:"职业咨询",STRESS_MANAGEMENT:"压力管理",GENERAL_COUNSELING:"综合咨询"},$=i=>U[i]||i,v=async()=>{var i,e;try{f.value=!0;const r={page:m.value-1,size:k.value};n.specialization&&(r.specialization=n.specialization),(n.priceRange[0]>0||n.priceRange[1]<1e3)&&(r.minRate=n.priceRange[0],r.maxRate=n.priceRange[1]),n.minRating>0&&(r.minRating=n.minRating),n.minYears&&(r.minYears=n.minYears);const u=await me(r);V.value=u.content||((i=u.data)==null?void 0:i.content)||[],x.value=u.totalElements||((e=u.data)==null?void 0:e.totalElements)||0}catch(r){console.error("Failed to fetch counselors:",r),M.error("获取咨询师列表失败")}finally{f.value=!1}},A=()=>{m.value=1,v()},w=()=>{n.specialization="",n.priceRange=[0,1e3],n.minRating=0,n.minYears=null,I.value="rating",m.value=1,v()},j=()=>{A()},J=()=>{v()},X=i=>{k.value=i,m.value=1,v()},q=i=>{m.value=i,v()},Y=i=>{h.push(`/counselors/${i}`)},K=i=>{if(!N.isLoggedIn){M.warning("请先登录"),h.push("/login");return}h.push(`/booking/${i}`)},Q=async()=>{await N.logoutUser(),h.push("/")};return ie(()=>{v()}),(i,e)=>{const r=d("router-link"),u=d("el-button"),C=d("el-option"),O=d("el-select"),y=d("el-form-item"),W=d("el-slider"),T=d("el-rate"),L=d("el-icon"),Z=d("el-form"),S=d("el-radio-button"),ee=d("el-radio-group"),te=d("el-avatar"),ae=d("el-empty"),le=d("el-pagination"),se=de("loading");return _(),p("div",ge,[l("nav",fe,[l("div",Ce,[t(r,{to:"/",class:"nav-brand"},{default:s(()=>[...e[7]||(e[7]=[l("h2",null,"心理咨询平台",-1)])]),_:1}),l("div",ye,[t(r,{to:"/",class:"nav-link"},{default:s(()=>[...e[8]||(e[8]=[o("首页",-1)])]),_:1}),E(N).isLoggedIn?(_(),p(z,{key:1},[t(r,{to:"/dashboard",class:"nav-link"},{default:s(()=>[...e[11]||(e[11]=[o("个人中心",-1)])]),_:1}),t(u,{onClick:Q,type:"text"},{default:s(()=>[...e[12]||(e[12]=[o("退出",-1)])]),_:1})],64)):(_(),p(z,{key:0},[t(r,{to:"/login",class:"nav-link"},{default:s(()=>[...e[9]||(e[9]=[o("登录",-1)])]),_:1}),t(r,{to:"/register",class:"nav-link nav-button"},{default:s(()=>[...e[10]||(e[10]=[o("注册",-1)])]),_:1})],64))])])]),l("div",Re,[l("div",be,[e[23]||(e[23]=l("div",{class:"page-header"},[l("h1",null,"寻找专业咨询师"),l("p",null,"选择最适合您的心理健康专家")],-1)),l("div",he,[t(Z,{model:n,inline:!0,class:"search-form"},{default:s(()=>[t(y,{label:"专业领域"},{default:s(()=>[t(O,{modelValue:n.specialization,"onUpdate:modelValue":e[0]||(e[0]=a=>n.specialization=a),placeholder:"选择专业领域",clearable:"",style:{width:"200px"}},{default:s(()=>[(_(),p(z,null,G(U,(a,R)=>t(C,{key:R,label:a,value:R},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),t(y,{label:"费用范围"},{default:s(()=>[t(W,{modelValue:n.priceRange,"onUpdate:modelValue":e[1]||(e[1]=a=>n.priceRange=a),range:"",min:0,max:1e3,step:50,style:{width:"200px"},onChange:j},null,8,["modelValue"]),l("span",ke," ¥"+c(n.priceRange[0])+" - ¥"+c(n.priceRange[1]),1)]),_:1}),t(y,{label:"最低评分"},{default:s(()=>[t(T,{modelValue:n.minRating,"onUpdate:modelValue":e[2]||(e[2]=a=>n.minRating=a),max:5,"show-score":"","text-color":"#ff9900","score-template":"{value}分以上"},null,8,["modelValue"])]),_:1}),t(y,{label:"经验年限"},{default:s(()=>[t(O,{modelValue:n.minYears,"onUpdate:modelValue":e[3]||(e[3]=a=>n.minYears=a),placeholder:"选择经验年限",clearable:"",style:{width:"150px"}},{default:s(()=>[t(C,{label:"1年以上",value:1}),t(C,{label:"3年以上",value:3}),t(C,{label:"5年以上",value:5}),t(C,{label:"10年以上",value:10})]),_:1},8,["modelValue"])]),_:1}),t(y,null,{default:s(()=>[t(u,{type:"primary",onClick:A,loading:f.value},{default:s(()=>[t(L,null,{default:s(()=>[t(E(ce))]),_:1}),e[13]||(e[13]=o(" 搜索 ",-1))]),_:1},8,["loading"]),t(u,{onClick:w},{default:s(()=>[...e[14]||(e[14]=[o("重置",-1)])]),_:1})]),_:1})]),_:1},8,["model"])]),l("div",Se,[e[19]||(e[19]=l("span",null,"排序方式：",-1)),t(ee,{modelValue:I.value,"onUpdate:modelValue":e[4]||(e[4]=a=>I.value=a),onChange:J},{default:s(()=>[t(S,{label:"rating"},{default:s(()=>[...e[15]||(e[15]=[o("评分最高",-1)])]),_:1}),t(S,{label:"sessions"},{default:s(()=>[...e[16]||(e[16]=[o("最受欢迎",-1)])]),_:1}),t(S,{label:"price_asc"},{default:s(()=>[...e[17]||(e[17]=[o("价格从低到高",-1)])]),_:1}),t(S,{label:"price_desc"},{default:s(()=>[...e[18]||(e[18]=[o("价格从高到低",-1)])]),_:1})]),_:1},8,["modelValue"])]),re((_(),p("div",Ee,[l("div",ze,[(_(!0),p(z,null,G(V.value,a=>{var R,D,P;return _(),p("div",{key:a.id,class:"counselor-card card",onClick:b=>Y(a.id)},[l("div",Ve,[t(te,{size:80,src:(R=a.user)==null?void 0:R.avatarUrl},{default:s(()=>{var b,B;return[o(c((B=(b=a.user)==null?void 0:b.username)==null?void 0:B.charAt(0)),1)]}),_:2},1032,["src"]),l("div",xe,[l("h3",null,c(((D=a.user)==null?void 0:D.realName)||((P=a.user)==null?void 0:P.username)),1),l("p",Ie,c($(a.specialization)),1),l("div",Le,[t(T,{"model-value":a.rating||0,disabled:"","show-score":"","text-color":"#ff9900"},null,8,["model-value"]),l("span",Ue,"("+c(a.totalSessions)+"次咨询)",1)])]),l("div",Ae,[l("span",we,"¥"+c(a.hourlyRate),1),e[20]||(e[20]=l("span",{class:"price-unit"},"/小时",-1))])]),l("div",Ye,[l("div",Oe,[t(L,null,{default:s(()=>[t(E(pe))]),_:1}),l("span",null,c(a.yearsOfExperience||0)+"年经验",1)]),l("div",Te,[t(L,null,{default:s(()=>[t(E(_e))]),_:1}),l("span",null,c(a.education||"暂无信息"),1)])]),l("div",De,[l("p",null,c(a.professionalSummary||"暂无简介"),1)]),l("div",Pe,[t(u,{type:"primary",disabled:!a.availableForBooking,onClick:H(b=>K(a.id),["stop"])},{default:s(()=>[o(c(a.availableForBooking?"立即预约":"暂不可约"),1)]),_:2},1032,["disabled","onClick"]),t(u,{onClick:H(b=>Y(a.id),["stop"])},{default:s(()=>[...e[21]||(e[21]=[o(" 查看详情 ",-1)])]),_:2},1032,["onClick"])])],8,Ne)}),128))]),!f.value&&V.value.length===0?(_(),p("div",Be,[t(ae,{description:"暂无符合条件的咨询师"},{default:s(()=>[t(u,{type:"primary",onClick:w},{default:s(()=>[...e[22]||(e[22]=[o("重置筛选条件",-1)])]),_:1})]),_:1})])):F("",!0)])),[[se,f.value]]),x.value>0?(_(),p("div",Fe,[t(le,{"current-page":m.value,"onUpdate:currentPage":e[5]||(e[5]=a=>m.value=a),"page-size":k.value,"onUpdate:pageSize":e[6]||(e[6]=a=>k.value=a),"page-sizes":[12,24,48],total:x.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:X,onCurrentChange:q},null,8,["current-page","page-size","total"])])):F("",!0)])])])}}},Je=ve(Ge,[["__scopeId","data-v-2e8ba43d"]]);export{Je as default};
