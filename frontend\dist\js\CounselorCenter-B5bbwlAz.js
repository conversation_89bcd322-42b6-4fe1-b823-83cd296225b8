import{c as a,a as t,y as e,n as u,F as p,k as C,i as k,$ as T,r as b,z as f,o as L,E as l,m as d,U as E,h as V}from"./index-D4DFMkO1.js";import{_ as B}from"./_plugin-vue_export-helper-DlAUqK2U.js";const z={name:"CounselorCenter",setup(){const v=b(!1),s=b([]),m=b([]),o=f({totalSessions:0,totalClients:0,averageRating:0,totalEarnings:0}),g=f({availableForBooking:!0,autoConfirm:!1}),y=[{id:1,client:{name:"张女士"},scheduledTime:"2024-09-01T14:00:00",duration:60,consultationType:"VIDEO",status:"CONFIRMED"},{id:2,client:{name:"李先生"},scheduledTime:"2024-09-01T16:00:00",duration:50,consultationType:"VOICE",status:"CONFIRMED"}],n=[{id:1,client:{name:"王女士"},rating:5,comment:"非常专业，帮助很大，会继续咨询。",createdAt:"2024-08-30T10:00:00"},{id:2,client:{name:"陈先生"},rating:4,comment:"咨询师很耐心，给了很多实用的建议。",createdAt:"2024-08-29T15:30:00"}],r={totalSessions:156,totalClients:89,averageRating:4.8,totalEarnings:31200},S=i=>({PENDING:"status-pending",CONFIRMED:"status-confirmed",IN_PROGRESS:"status-in-progress",COMPLETED:"status-completed",CANCELLED:"status-cancelled"})[i]||"",_=i=>({PENDING:"待确认",CONFIRMED:"已确认",IN_PROGRESS:"进行中",COMPLETED:"已完成",CANCELLED:"已取消"})[i]||i,D=i=>({VIDEO:"视频咨询",VOICE:"语音咨询",TEXT:"文字咨询"})[i]||i,w=i=>new Date(i).toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit"}),N=i=>new Date(i).toLocaleDateString("zh-CN"),A=async()=>{v.value=!0;try{await new Promise(i=>setTimeout(i,1e3)),s.value=y,m.value=n,Object.assign(o,r)}catch{l.error("加载数据失败")}finally{v.value=!1}},R=()=>{l.info("时间设置功能开发中...")},I=()=>{l.info("资料更新功能开发中...")},x=()=>{l.info("收入报告功能开发中...")},O=()=>{l.info("客户管理功能开发中...")},P=i=>{l.info("咨询会话功能开发中...")},F=i=>{l.info("预约详情功能开发中...")},M=async()=>{try{await new Promise(c=>setTimeout(c,500));const i=g.availableForBooking?"开启":"关闭";l.success(`已${i}接受新预约`)}catch{l.error("更新状态失败")}},h=async()=>{try{await new Promise(i=>setTimeout(i,500)),l.success("设置已更新")}catch{l.error("更新设置失败")}};return L(()=>{A()}),{loading:v,stats:o,todayAppointments:s,recentReviews:m,counselorSettings:g,getStatusClass:S,getStatusText:_,getConsultationTypeText:D,formatTime:w,formatDate:N,setAvailability:R,updateProfile:I,viewEarnings:x,manageClients:O,startSession:P,viewAppointmentDetails:F,updateAvailability:M,updateSettings:h}}},G={class:"counselor-center-container"},U={class:"stats-overview"},j={class:"stat-card"},q={class:"stat-content"},X={class:"stat-card"},H={class:"stat-content"},J={class:"stat-card"},K={class:"stat-content"},Q={class:"stat-card"},W={class:"stat-content"},Y={class:"quick-actions"},Z={class:"action-buttons"},$={class:"today-appointments"},tt={key:0,class:"empty-state"},st={key:1,class:"appointment-list"},nt={class:"appointment-time"},ot={class:"time"},it={class:"duration"},et={class:"client-info"},at={class:"consultation-type"},lt={class:"appointment-status"},dt={class:"appointment-actions"},rt=["onClick"],ct=["onClick"],ut={class:"recent-reviews"},vt={key:0,class:"empty-state"},mt={key:1,class:"review-list"},gt={class:"review-header"},pt={class:"client-info"},Ct={class:"client-name"},bt={class:"review-date"},yt={class:"rating"},kt={class:"stars"},Tt={class:"rating-value"},ft={class:"review-content"},Et={class:"status-settings"},St={class:"setting-item"},_t={class:"setting-label"},Dt={class:"setting-item"},wt={class:"setting-label"};function Nt(v,s,m,o,g,y){return d(),a("div",G,[s[32]||(s[32]=t("div",{class:"center-header"},[t("h2",null,"咨询师中心"),t("p",{class:"subtitle"},"管理您的咨询服务和客户")],-1)),t("div",U,[t("div",j,[s[9]||(s[9]=t("div",{class:"stat-icon"},"📊",-1)),t("div",q,[t("h3",null,e(o.stats.totalSessions),1),s[8]||(s[8]=t("p",null,"总咨询次数",-1))])]),t("div",X,[s[11]||(s[11]=t("div",{class:"stat-icon"},"👥",-1)),t("div",H,[t("h3",null,e(o.stats.totalClients),1),s[10]||(s[10]=t("p",null,"服务客户数",-1))])]),t("div",J,[s[13]||(s[13]=t("div",{class:"stat-icon"},"⭐",-1)),t("div",K,[t("h3",null,e(o.stats.averageRating),1),s[12]||(s[12]=t("p",null,"平均评分",-1))])]),t("div",Q,[s[15]||(s[15]=t("div",{class:"stat-icon"},"💰",-1)),t("div",W,[t("h3",null,"¥"+e(o.stats.totalEarnings),1),s[14]||(s[14]=t("p",null,"总收入",-1))])])]),t("div",Y,[s[20]||(s[20]=t("h3",null,"快捷操作",-1)),t("div",Z,[t("button",{onClick:s[0]||(s[0]=(...n)=>o.setAvailability&&o.setAvailability(...n)),class:"action-btn"},[...s[16]||(s[16]=[t("span",{class:"btn-icon"},"🕒",-1),u(" 设置可预约时间 ",-1)])]),t("button",{onClick:s[1]||(s[1]=(...n)=>o.updateProfile&&o.updateProfile(...n)),class:"action-btn"},[...s[17]||(s[17]=[t("span",{class:"btn-icon"},"👤",-1),u(" 更新个人资料 ",-1)])]),t("button",{onClick:s[2]||(s[2]=(...n)=>o.viewEarnings&&o.viewEarnings(...n)),class:"action-btn"},[...s[18]||(s[18]=[t("span",{class:"btn-icon"},"📈",-1),u(" 查看收入报告 ",-1)])]),t("button",{onClick:s[3]||(s[3]=(...n)=>o.manageClients&&o.manageClients(...n)),class:"action-btn"},[...s[19]||(s[19]=[t("span",{class:"btn-icon"},"📋",-1),u(" 客户管理 ",-1)])])])]),t("div",$,[s[22]||(s[22]=t("h3",null,"今日预约",-1)),o.todayAppointments.length===0?(d(),a("div",tt,[...s[21]||(s[21]=[t("p",null,"今天暂无预约",-1)])])):(d(),a("div",st,[(d(!0),a(p,null,C(o.todayAppointments,n=>(d(),a("div",{key:n.id,class:"appointment-item"},[t("div",nt,[t("span",ot,e(o.formatTime(n.scheduledTime)),1),t("span",it,e(n.duration)+"分钟",1)]),t("div",et,[t("h4",null,e(n.client.name),1),t("p",at,e(o.getConsultationTypeText(n.consultationType)),1)]),t("div",lt,[t("span",{class:E(["status-badge",o.getStatusClass(n.status)])},e(o.getStatusText(n.status)),3)]),t("div",dt,[n.status==="CONFIRMED"?(d(),a("button",{key:0,onClick:r=>o.startSession(n.id),class:"btn btn-primary btn-sm"}," 开始咨询 ",8,rt)):V("",!0),t("button",{onClick:r=>o.viewAppointmentDetails(n.id),class:"btn btn-outline btn-sm"}," 查看详情 ",8,ct)])]))),128))]))]),t("div",ut,[s[24]||(s[24]=t("h3",null,"最近评价",-1)),o.recentReviews.length===0?(d(),a("div",vt,[...s[23]||(s[23]=[t("p",null,"暂无评价",-1)])])):(d(),a("div",mt,[(d(!0),a(p,null,C(o.recentReviews,n=>(d(),a("div",{key:n.id,class:"review-item"},[t("div",gt,[t("div",pt,[t("span",Ct,e(n.client.name),1),t("span",bt,e(o.formatDate(n.createdAt)),1)]),t("div",yt,[t("div",kt,[(d(),a(p,null,C(5,r=>t("span",{key:r,class:E(["star",{active:r<=n.rating}])}," ⭐ ",2)),64))]),t("span",Tt,e(n.rating)+"/5",1)])]),t("div",ft,[t("p",null,e(n.comment),1)])]))),128))]))]),s[33]||(s[33]=t("div",{class:"earnings-chart"},[t("h3",null,"收入趋势"),t("div",{class:"chart-placeholder"},[t("p",null,"📈 收入图表功能开发中...")])],-1)),t("div",Et,[s[31]||(s[31]=t("h3",null,"状态设置",-1)),t("div",St,[t("label",_t,[k(t("input",{type:"checkbox","onUpdate:modelValue":s[4]||(s[4]=n=>o.counselorSettings.availableForBooking=n),onChange:s[5]||(s[5]=(...n)=>o.updateAvailability&&o.updateAvailability(...n))},null,544),[[T,o.counselorSettings.availableForBooking]]),s[25]||(s[25]=t("span",{class:"checkmark"},null,-1)),s[26]||(s[26]=u(" 接受新预约 ",-1))]),s[27]||(s[27]=t("p",{class:"setting-description"},"关闭后，客户将无法预约您的咨询服务",-1))]),t("div",Dt,[t("label",wt,[k(t("input",{type:"checkbox","onUpdate:modelValue":s[6]||(s[6]=n=>o.counselorSettings.autoConfirm=n),onChange:s[7]||(s[7]=(...n)=>o.updateSettings&&o.updateSettings(...n))},null,544),[[T,o.counselorSettings.autoConfirm]]),s[28]||(s[28]=t("span",{class:"checkmark"},null,-1)),s[29]||(s[29]=u(" 自动确认预约 ",-1))]),s[30]||(s[30]=t("p",{class:"setting-description"},"开启后，客户预约将自动确认，无需手动处理",-1))])])])}const It=B(z,[["render",Nt],["__scopeId","data-v-62e446eb"]]);export{It as default};
