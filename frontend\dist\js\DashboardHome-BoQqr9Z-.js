import{u as U,r as E,z as W,o as j,c as p,a as s,y as r,f as l,g as M,w as n,h as D,d as e,e as f,i as $,j as q,F as X,k as Y,l as J,m as u,n as v,s as K,X as Q,O as b,x as O,t as Z,v as ss,B as ts,Y as N,p as es,M as os}from"./index-D4DFMkO1.js";import{_ as ns}from"./_plugin-vue_export-helper-DlAUqK2U.js";const as={class:"dashboard-home"},ls={class:"welcome-section card"},rs={class:"welcome-content"},is={class:"welcome-actions"},ds={class:"stats-grid"},us={class:"stat-card card"},cs={class:"stat-icon"},_s={class:"stat-content"},ps={class:"stat-card card"},ms={class:"stat-icon"},fs={class:"stat-content"},vs={class:"stat-card card"},hs={class:"stat-icon"},gs={class:"stat-content"},ys={key:0,class:"stat-card card"},Cs={class:"stat-icon"},Ns={class:"stat-content"},ks={class:"recent-section"},Ds={class:"recent-appointments card"},As={class:"section-header"},Ss={class:"appointments-list"},Es={class:"appointment-info"},Ms={class:"appointment-avatar"},bs={class:"appointment-details"},Os={class:"appointment-status"},Rs={key:0,class:"empty-state"},Ts={class:"quick-actions card"},ws={class:"actions-list"},xs={__name:"DashboardHome",setup(Is){const h=J(),i=U(),g=E(!1),k=E([]),c=W({totalAppointments:0,completedSessions:0,upcomingAppointments:0,averageRating:null}),R=()=>{const o=new Date().getHours();let t="";return o<12?t="早上好":o<18?t="下午好":t="晚上好",i.isCounselor?`${t}！今天有什么新的咨询安排吗？`:`${t}！希望您今天心情愉快！`},T=o=>{var t,a,d;return i.isCounselor?(t=o.user)==null?void 0:t.avatarUrl:(d=(a=o.counselor)==null?void 0:a.user)==null?void 0:d.avatarUrl},A=o=>{var t,a,d,m,y,C;return i.isCounselor?((t=o.user)==null?void 0:t.realName)||((a=o.user)==null?void 0:a.username)||"用户":((m=(d=o.counselor)==null?void 0:d.user)==null?void 0:m.realName)||((C=(y=o.counselor)==null?void 0:y.user)==null?void 0:C.username)||"咨询师"},w=o=>os(o).format("MM月DD日 HH:mm"),x=o=>({PENDING:"warning",CONFIRMED:"success",IN_PROGRESS:"primary",COMPLETED:"info",CANCELLED:"danger",NO_SHOW:"danger"})[o]||"info",I=o=>({PENDING:"待确认",CONFIRMED:"已确认",IN_PROGRESS:"进行中",COMPLETED:"已完成",CANCELLED:"已取消",NO_SHOW:"未出席"})[o]||o,L=async()=>{try{c.totalAppointments=12,c.completedSessions=8,c.upcomingAppointments=2,i.isCounselor&&(c.averageRating=4.8)}catch(o){console.error("Failed to fetch stats:",o)}},H=async()=>{try{g.value=!0,k.value=[]}catch(o){console.error("Failed to fetch recent appointments:",o)}finally{g.value=!1}},P=()=>{h.push("/dashboard/profile")},F=()=>{h.push("/dashboard/appointments")},z=()=>{h.push("/dashboard/sessions")},B=()=>{h.push("/dashboard/counselor-apply")};return j(()=>{L(),H()}),(o,t)=>{var S;const a=f("el-icon"),d=f("el-button"),m=f("router-link"),y=f("el-avatar"),C=f("el-tag"),G=f("el-empty"),V=q("loading");return u(),p("div",as,[s("div",ls,[s("div",rs,[s("h2",null,"欢迎回来，"+r(((S=l(i).userInfo)==null?void 0:S.realName)||l(i).userName)+"！",1),s("p",null,r(R()),1)]),s("div",is,[l(i).isCounselor?(u(),M(m,{key:1,to:"/dashboard/counselor-center"},{default:n(()=>[e(d,{type:"primary",size:"large"},{default:n(()=>[e(a,null,{default:n(()=>[e(l(Q))]),_:1}),t[1]||(t[1]=v(" 咨询师中心 ",-1))]),_:1})]),_:1})):(u(),M(m,{key:0,to:"/counselors"},{default:n(()=>[e(d,{type:"primary",size:"large"},{default:n(()=>[e(a,null,{default:n(()=>[e(l(K))]),_:1}),t[0]||(t[0]=v(" 寻找咨询师 ",-1))]),_:1})]),_:1}))])]),s("div",ds,[s("div",us,[s("div",cs,[e(a,null,{default:n(()=>[e(l(b))]),_:1})]),s("div",_s,[s("h3",null,r(c.totalAppointments),1),t[2]||(t[2]=s("p",null,"总预约数",-1))])]),s("div",ps,[s("div",ms,[e(a,null,{default:n(()=>[e(l(O))]),_:1})]),s("div",fs,[s("h3",null,r(c.completedSessions),1),t[3]||(t[3]=s("p",null,"完成咨询",-1))])]),s("div",vs,[s("div",hs,[e(a,null,{default:n(()=>[e(l(Z))]),_:1})]),s("div",gs,[s("h3",null,r(c.upcomingAppointments),1),t[4]||(t[4]=s("p",null,"待完成预约",-1))])]),l(i).isCounselor?(u(),p("div",ys,[s("div",Cs,[e(a,null,{default:n(()=>[e(l(ss))]),_:1})]),s("div",Ns,[s("h3",null,r(c.averageRating||"暂无"),1),t[5]||(t[5]=s("p",null,"平均评分",-1))])])):D("",!0)]),s("div",ks,[s("div",Ds,[s("div",As,[t[7]||(t[7]=s("h3",null,"最近预约",-1)),e(m,{to:"/dashboard/appointments"},{default:n(()=>[e(d,{type:"text"},{default:n(()=>[...t[6]||(t[6]=[v("查看全部",-1)])]),_:1})]),_:1})]),$((u(),p("div",Ss,[(u(!0),p(X,null,Y(k.value,_=>(u(),p("div",{key:_.id,class:"appointment-item"},[s("div",Es,[s("div",Ms,[e(y,{size:40,src:T(_)},{default:n(()=>[v(r(A(_).charAt(0)),1)]),_:2},1032,["src"])]),s("div",bs,[s("h4",null,r(A(_)),1),s("p",null,r(w(_.scheduledTime)),1)])]),s("div",Os,[e(C,{type:x(_.status)},{default:n(()=>[v(r(I(_.status)),1)]),_:2},1032,["type"])])]))),128)),!g.value&&k.value.length===0?(u(),p("div",Rs,[e(G,{description:"暂无预约记录","image-size":80})])):D("",!0)])),[[V,g.value]])]),s("div",Ts,[t[12]||(t[12]=s("div",{class:"section-header"},[s("h3",null,"快捷操作")],-1)),s("div",ws,[s("div",{class:"action-item",onClick:P},[e(a,null,{default:n(()=>[e(l(ts))]),_:1}),t[8]||(t[8]=s("span",null,"编辑个人信息",-1)),e(a,null,{default:n(()=>[e(l(N))]),_:1})]),s("div",{class:"action-item",onClick:F},[e(a,null,{default:n(()=>[e(l(b))]),_:1}),t[9]||(t[9]=s("span",null,"管理预约",-1)),e(a,null,{default:n(()=>[e(l(N))]),_:1})]),s("div",{class:"action-item",onClick:z},[e(a,null,{default:n(()=>[e(l(O))]),_:1}),t[10]||(t[10]=s("span",null,"查看咨询记录",-1)),e(a,null,{default:n(()=>[e(l(N))]),_:1})]),l(i).isCounselor?D("",!0):(u(),p("div",{key:0,class:"action-item",onClick:B},[e(a,null,{default:n(()=>[e(l(es))]),_:1}),t[11]||(t[11]=s("span",null,"申请成为咨询师",-1)),e(a,null,{default:n(()=>[e(l(N))]),_:1})]))])])])])}}},Ps=ns(xs,[["__scopeId","data-v-dda3b879"]]);export{Ps as default};
