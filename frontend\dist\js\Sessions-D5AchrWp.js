import{c as l,a as t,h as f,y as a,i as D,_ as h,F as w,k as R,b as Q,d as W,w as Y,e as Z,r as u,z as $,S as tt,o as st,E as S,l as et,m as i,n as nt,U as I}from"./index-D4DFMkO1.js";import{_ as ot}from"./_plugin-vue_export-helper-DlAUqK2U.js";const at={name:"Sessions",setup(){const V=et(),s=u(!1),v=u([]),n=u([]),y=u(""),T=u(""),b=u(""),e=u(1);u(10);const d=u(1),E=$({totalSessions:0,totalDuration:0,counselorCount:0,averageRating:0}),M=[{id:1,counselor:{id:1,name:"张心理师",specialization:"焦虑症治疗",avatarUrl:""},startTime:"2024-08-28T14:00:00",endTime:"2024-08-28T15:00:00",consultationType:"VIDEO",amount:200,summary:"本次咨询主要讨论了工作压力问题，提供了一些放松技巧和时间管理建议。",userRating:5,userComment:"非常专业，帮助很大！",hasRecording:!0},{id:2,counselor:{id:2,name:"李咨询师",specialization:"情感咨询",avatarUrl:""},startTime:"2024-08-25T10:00:00",endTime:"2024-08-25T11:00:00",consultationType:"VOICE",amount:180,summary:"探讨了人际关系中的沟通问题，学习了有效的沟通技巧。",userRating:4,userComment:"很有帮助，会继续咨询。",hasRecording:!1}],x=[{id:1,name:"张心理师"},{id:2,name:"李咨询师"}],z=tt(()=>{let o=v.value;if(y.value&&(o=o.filter(r=>r.counselor.id==y.value)),T.value){const r=new Date;o=o.filter(p=>{const _=new Date(p.startTime),c=Math.floor((r-_)/(1e3*60*60*24));switch(T.value){case"week":return c<=7;case"month":return c<=30;case"quarter":return c<=90;case"year":return c<=365;default:return!0}})}return b.value&&(o=o.filter(r=>r.consultationType===b.value)),o}),O=o=>({VIDEO:"type-video",VOICE:"type-voice",TEXT:"type-text"})[o]||"",N=o=>({VIDEO:"视频",VOICE:"语音",TEXT:"文字"})[o]||o,U=o=>new Date(o).toLocaleDateString("zh-CN"),q=o=>new Date(o).toLocaleString("zh-CN",{month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"}),B=(o,r)=>{const p=new Date(o),_=new Date(r),c=Math.floor((_-p)/(1e3*60)),g=Math.floor(c/60),C=c%60;return g>0?`${g}小时${C}分钟`:`${C}分钟`},L=()=>{const o=v.value.length,r=v.value.reduce((m,k)=>{const J=new Date(k.startTime),K=new Date(k.endTime);return m+Math.floor((K-J)/(1e3*60))},0),p=Math.round(r/60*10)/10,c=new Set(v.value.map(m=>m.counselor.id)).size,g=v.value.filter(m=>m.userRating),C=g.length>0?Math.round(g.reduce((m,k)=>m+k.userRating,0)/g.length*10)/10:0;Object.assign(E,{totalSessions:o,totalDuration:p,counselorCount:c,averageRating:C})},P=async()=>{s.value=!0;try{await new Promise(o=>setTimeout(o,1e3)),v.value=M,n.value=x,L()}catch{S.error("加载咨询记录失败")}finally{s.value=!1}},X=()=>{e.value=1},A=o=>{S.info("详情页面开发中...")},F=o=>{S.info("录音播放功能开发中...")},j=o=>{S.info("评价功能开发中...")},G=o=>{V.push(`/booking/${o}`)},H=o=>{o>=1&&o<=d.value&&(e.value=o,P())};return st(()=>{P()}),{loading:s,sessions:v,counselors:n,filteredSessions:z,stats:E,selectedCounselor:y,selectedTimeRange:T,selectedType:b,currentPage:e,totalPages:d,getTypeClass:O,getTypeText:N,formatDate:U,formatDateTime:q,calculateDuration:B,filterSessions:X,viewSessionDetails:A,playRecording:F,rateSession:j,bookAgain:G,changePage:H}}},lt={class:"sessions-container"},it={class:"stats-cards"},rt={class:"stat-card"},dt={class:"stat-content"},ct={class:"stat-card"},ut={class:"stat-content"},vt={class:"stat-card"},mt={class:"stat-content"},gt={class:"stat-card"},ft={class:"stat-content"},bt={class:"filters"},pt={class:"filter-group"},yt=["value"],Tt={class:"filter-group"},_t={class:"filter-group"},Ct={class:"sessions-list"},kt={key:0,class:"loading"},St={key:1,class:"empty-state"},Dt={key:2,class:"session-cards"},ht={class:"session-header"},wt={class:"counselor-info"},Rt=["src","alt"],Vt={class:"counselor-details"},Et={class:"specialization"},Pt={class:"session-meta"},It={class:"session-date"},Mt={class:"session-details"},xt={class:"detail-row"},zt={class:"value"},Ot={class:"detail-row"},Nt={class:"value"},Ut={class:"detail-row"},qt={class:"value"},Bt={class:"detail-row"},Lt={class:"value price"},Xt={key:0,class:"session-summary"},At={key:1,class:"session-rating"},Ft={class:"rating-info"},jt={class:"stars"},Gt={class:"rating-value"},Ht={key:0,class:"rating-comment"},Jt={class:"session-actions"},Kt=["onClick"],Qt=["onClick"],Wt=["onClick"],Yt=["onClick"],Zt={key:0,class:"pagination"},$t=["disabled"],ts={class:"page-info"},ss=["disabled"];function es(V,s,v,n,y,T){const b=Z("router-link");return i(),l("div",lt,[s[34]||(s[34]=t("div",{class:"sessions-header"},[t("h2",null,"咨询记录"),t("p",{class:"subtitle"},"查看您的咨询会话历史记录")],-1)),t("div",it,[t("div",rt,[s[9]||(s[9]=t("div",{class:"stat-icon"},"📊",-1)),t("div",dt,[t("h3",null,a(n.stats.totalSessions),1),s[8]||(s[8]=t("p",null,"总咨询次数",-1))])]),t("div",ct,[s[11]||(s[11]=t("div",{class:"stat-icon"},"⏱️",-1)),t("div",ut,[t("h3",null,a(n.stats.totalDuration)+"h",1),s[10]||(s[10]=t("p",null,"总咨询时长",-1))])]),t("div",vt,[s[13]||(s[13]=t("div",{class:"stat-icon"},"👥",-1)),t("div",mt,[t("h3",null,a(n.stats.counselorCount),1),s[12]||(s[12]=t("p",null,"咨询师数量",-1))])]),t("div",gt,[s[15]||(s[15]=t("div",{class:"stat-icon"},"⭐",-1)),t("div",ft,[t("h3",null,a(n.stats.averageRating),1),s[14]||(s[14]=t("p",null,"平均评分",-1))])])]),t("div",bt,[t("div",pt,[s[17]||(s[17]=t("label",null,"咨询师：",-1)),D(t("select",{"onUpdate:modelValue":s[0]||(s[0]=e=>n.selectedCounselor=e),onChange:s[1]||(s[1]=(...e)=>n.filterSessions&&n.filterSessions(...e)),class:"filter-select"},[s[16]||(s[16]=t("option",{value:""},"全部咨询师",-1)),(i(!0),l(w,null,R(n.counselors,e=>(i(),l("option",{key:e.id,value:e.id},a(e.name),9,yt))),128))],544),[[h,n.selectedCounselor]])]),t("div",Tt,[s[19]||(s[19]=t("label",null,"时间范围：",-1)),D(t("select",{"onUpdate:modelValue":s[2]||(s[2]=e=>n.selectedTimeRange=e),onChange:s[3]||(s[3]=(...e)=>n.filterSessions&&n.filterSessions(...e)),class:"filter-select"},[...s[18]||(s[18]=[Q('<option value="" data-v-59cbb775>全部时间</option><option value="week" data-v-59cbb775>最近一周</option><option value="month" data-v-59cbb775>最近一月</option><option value="quarter" data-v-59cbb775>最近三月</option><option value="year" data-v-59cbb775>最近一年</option>',5)])],544),[[h,n.selectedTimeRange]])]),t("div",_t,[s[21]||(s[21]=t("label",null,"咨询方式：",-1)),D(t("select",{"onUpdate:modelValue":s[4]||(s[4]=e=>n.selectedType=e),onChange:s[5]||(s[5]=(...e)=>n.filterSessions&&n.filterSessions(...e)),class:"filter-select"},[...s[20]||(s[20]=[t("option",{value:""},"全部方式",-1),t("option",{value:"VIDEO"},"视频咨询",-1),t("option",{value:"VOICE"},"语音咨询",-1),t("option",{value:"TEXT"},"文字咨询",-1)])],544),[[h,n.selectedType]])])]),t("div",Ct,[n.loading?(i(),l("div",kt,[...s[22]||(s[22]=[t("p",null,"加载中...",-1)])])):n.filteredSessions.length===0?(i(),l("div",St,[s[24]||(s[24]=t("div",{class:"empty-icon"},"💬",-1)),s[25]||(s[25]=t("h3",null,"暂无咨询记录",-1)),s[26]||(s[26]=t("p",null,"您还没有完成任何咨询会话",-1)),W(b,{to:"/counselors",class:"btn btn-primary"},{default:Y(()=>[...s[23]||(s[23]=[nt(" 开始咨询 ",-1)])]),_:1})])):(i(),l("div",Dt,[(i(!0),l(w,null,R(n.filteredSessions,e=>(i(),l("div",{key:e.id,class:"session-card"},[t("div",ht,[t("div",wt,[t("img",{src:e.counselor.avatarUrl||"/default-avatar.png",alt:e.counselor.name,class:"counselor-avatar"},null,8,Rt),t("div",Vt,[t("h4",null,a(e.counselor.name),1),t("p",Et,a(e.counselor.specialization),1)])]),t("div",Pt,[t("span",It,a(n.formatDate(e.startTime)),1),t("span",{class:I(["session-type",n.getTypeClass(e.consultationType)])},a(n.getTypeText(e.consultationType)),3)])]),t("div",Mt,[t("div",xt,[s[27]||(s[27]=t("span",{class:"label"},"开始时间：",-1)),t("span",zt,a(n.formatDateTime(e.startTime)),1)]),t("div",Ot,[s[28]||(s[28]=t("span",{class:"label"},"结束时间：",-1)),t("span",Nt,a(n.formatDateTime(e.endTime)),1)]),t("div",Ut,[s[29]||(s[29]=t("span",{class:"label"},"咨询时长：",-1)),t("span",qt,a(n.calculateDuration(e.startTime,e.endTime)),1)]),t("div",Bt,[s[30]||(s[30]=t("span",{class:"label"},"咨询费用：",-1)),t("span",Lt,"¥"+a(e.amount),1)])]),e.summary?(i(),l("div",Xt,[s[31]||(s[31]=t("h5",null,"咨询摘要",-1)),t("p",null,a(e.summary),1)])):f("",!0),e.userRating?(i(),l("div",At,[t("div",Ft,[s[32]||(s[32]=t("span",{class:"label"},"我的评分：",-1)),t("div",jt,[(i(),l(w,null,R(5,d=>t("span",{key:d,class:I(["star",{active:d<=e.userRating}])}," ⭐ ",2)),64))]),t("span",Gt,a(e.userRating)+"/5",1)]),e.userComment?(i(),l("div",Ht,[s[33]||(s[33]=t("span",{class:"label"},"我的评价：",-1)),t("p",null,a(e.userComment),1)])):f("",!0)])):f("",!0),t("div",Jt,[t("button",{onClick:d=>n.viewSessionDetails(e.id),class:"btn btn-outline btn-sm"}," 查看详情 ",8,Kt),e.hasRecording?(i(),l("button",{key:0,onClick:d=>n.playRecording(e.id),class:"btn btn-secondary btn-sm"}," 播放录音 ",8,Qt)):f("",!0),e.userRating?f("",!0):(i(),l("button",{key:1,onClick:d=>n.rateSession(e.id),class:"btn btn-primary btn-sm"}," 评价咨询 ",8,Wt)),t("button",{onClick:d=>n.bookAgain(e.counselor.id),class:"btn btn-success btn-sm"}," 再次预约 ",8,Yt)])]))),128))]))]),n.totalPages>1?(i(),l("div",Zt,[t("button",{onClick:s[6]||(s[6]=e=>n.changePage(n.currentPage-1)),disabled:n.currentPage===1,class:"btn btn-outline btn-sm"}," 上一页 ",8,$t),t("span",ts," 第 "+a(n.currentPage)+" 页，共 "+a(n.totalPages)+" 页 ",1),t("button",{onClick:s[7]||(s[7]=e=>n.changePage(n.currentPage+1)),disabled:n.currentPage===n.totalPages,class:"btn btn-outline btn-sm"}," 下一页 ",8,ss)])):f("",!0)])}const as=ot(at,[["render",es],["__scopeId","data-v-59cbb775"]]);export{as as default};
