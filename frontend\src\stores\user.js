import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { login, register, getUserInfo, logout } from '@/api/auth'
import { ElMessage } from 'element-plus'

export const useUserStore = defineStore('user', () => {
  // 状态
  const token = ref(localStorage.getItem('token') || '')
  const userInfo = ref(null)
  const counselorInfo = ref(null)

  // 计算属性
  const isLoggedIn = computed(() => !!token.value)
  const isCounselor = computed(() => !!counselorInfo.value)
  const userName = computed(() => userInfo.value?.username || '')
  const userAvatar = computed(() => userInfo.value?.avatarUrl || '')

  // 登录
  const loginUser = async (credentials) => {
    try {
      const response = await login(credentials)
      const { token: newToken, user } = response.data
      
      token.value = newToken
      userInfo.value = user
      localStorage.setItem('token', newToken)
      
      ElMessage.success('登录成功')
      return true
    } catch (error) {
      ElMessage.error(error.response?.data?.message || '登录失败')
      return false
    }
  }

  // 注册
  const registerUser = async (userData) => {
    try {
      await register(userData)
      ElMessage.success('注册成功，请登录')
      return true
    } catch (error) {
      ElMessage.error(error.response?.data?.message || '注册失败')
      return false
    }
  }

  // 登出
  const logoutUser = async (showMessage = true) => {
    try {
      await logout()
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      token.value = ''
      userInfo.value = null
      counselorInfo.value = null
      localStorage.removeItem('token')
      if (showMessage) {
        ElMessage.success('已退出登录')
      }
    }
  }

  // 获取用户信息
  const fetchUserInfo = async () => {
    try {
      const response = await getUserInfo()
      userInfo.value = response.data
      return response.data
    } catch (error) {
      console.error('Failed to fetch user info:', error)
      // 如果获取用户信息失败，可能token已过期，静默清除token
      if (error.response?.status === 401) {
        token.value = ''
        userInfo.value = null
        counselorInfo.value = null
        localStorage.removeItem('token')
      }
      return null
    }
  }

  // 初始化用户
  const initUser = async () => {
    if (token.value && !userInfo.value) {
      await fetchUserInfo()
    }
  }

  // 更新用户信息
  const updateUserInfo = (newUserInfo) => {
    userInfo.value = { ...userInfo.value, ...newUserInfo }
  }

  // 设置咨询师信息
  const setCounselorInfo = (info) => {
    counselorInfo.value = info
  }

  return {
    // 状态
    token,
    userInfo,
    counselorInfo,
    
    // 计算属性
    isLoggedIn,
    isCounselor,
    userName,
    userAvatar,
    
    // 方法
    loginUser,
    registerUser,
    logoutUser,
    fetchUserInfo,
    initUser,
    updateUserInfo,
    setCounselorInfo
  }
})
